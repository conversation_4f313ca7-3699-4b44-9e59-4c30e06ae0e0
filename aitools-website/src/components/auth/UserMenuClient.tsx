'use client';

import { useState } from 'react';
import { useSession, signOut } from 'next-auth/react';
import { Fa<PERSON>ser, FaHeart, FaPlus, FaCog, FaSignOutAlt, FaSignInAlt, FaChevronDown, FaList } from 'react-icons/fa';
import { useRouter, Link as NextLink } from '@/i18n/routing';
import { useTranslations, useLocale } from 'next-intl';
import LoginModal from './LoginModal';
import { Locale } from '@/i18n/config';


const NavLink = ({ children, href, locale }: { children: React.ReactNode; href: string; locale: string }) => (
  <NextLink
    href={href}
    locale={locale}
    className="w-full flex items-center gap-3 px-4 py-2 text-sm text-gray-700 hover:bg-gray-100 transition-colors"
  >
    {children}
  </NextLink>
);

export default function UserMenuClient({ locale }: { locale: Locale }) {
  const { data: session, status } = useSession();
  const router = useRouter();
  const [isLoginModalOpen, setIsLoginModalOpen] = useState(false);
  const [isMenuOpen, setIsMenuOpen] = useState(false);

  const t = useTranslations('common');

  const handleSignOut = async () => {
    await signOut({ callbackUrl: '/' });
  };

  const handleNavigation = (path: string) => {
    setIsMenuOpen(false);
    router.push(path);
  };

  // 如果正在加载，显示加载状态
  if (status === 'loading') {
    return (
      <button
        className="px-3 py-2 text-sm text-gray-600 bg-gray-100 rounded-lg animate-pulse"
        disabled
      >
        {t('loading')}
      </button>
    );
  }

  // 如果未登录，显示登录按钮
  if (!session) {
    return (
      <>
        <button
          className="flex items-center gap-2 px-3 py-2 text-sm border border-blue-300 text-blue-700 rounded-lg hover:bg-blue-50 transition-colors"
          onClick={() => setIsLoginModalOpen(true)}
        >
          <FaSignInAlt />
          {t('login')}
        </button>
        <LoginModal isOpen={isLoginModalOpen} onClose={() => setIsLoginModalOpen(false)} />
      </>
    );
  }

  // 如果已登录，显示用户菜单
  return (
    <div className="relative">
      <button
        className="flex items-center gap-2 p-1 rounded-lg hover:bg-gray-100 transition-colors"
        onClick={() => setIsMenuOpen(!isMenuOpen)}
      >
        <div className="w-8 h-8 rounded-full bg-gray-300 flex items-center justify-center overflow-hidden">
          {session.user?.image ? (
            <img
              src={session.user.image}
              alt={session.user.name || ''}
              className="w-full h-full object-cover"
            />
          ) : (
            <span className="text-sm font-medium text-gray-600">
              {session.user?.name?.charAt(0) || 'U'}
            </span>
          )}
        </div>
        <span className="text-sm hidden md:block">
          {session.user?.name}
        </span>
        <FaChevronDown className={`text-xs transition-transform ${isMenuOpen ? 'rotate-180' : ''}`} />
      </button>

      {isMenuOpen && (
        <>
          {/* Backdrop */}
          <div
            className="fixed inset-0 z-10"
            onClick={() => setIsMenuOpen(false)}
          />

          {/* Menu */}
          <div 
            className="absolute right-0 mt-2 w-64 bg-white rounded-lg shadow-lg border z-20"
            onClick={() => setIsMenuOpen(!isMenuOpen)}
          >
            {/* 用户信息 */}
            <div className="p-4 border-b">
              <div className="flex items-center gap-3">
                <div className="w-10 h-10 rounded-full bg-gray-300 flex items-center justify-center overflow-hidden">
                  {session.user?.image ? (
                    <img
                      src={session.user.image}
                      alt={session.user.name || ''}
                      className="w-full h-full object-cover"
                    />
                  ) : (
                    <span className="text-lg font-medium text-gray-600">
                      {session.user?.name?.charAt(0) || 'U'}
                    </span>
                  )}
                </div>
                <div>
                  <p className="font-medium text-sm">
                    {session.user?.name}
                  </p>
                  <p className="text-gray-500 text-xs">
                    {session.user?.email}
                  </p>
                  {session.user?.role === 'admin' && (
                    <span className="inline-block mt-1 px-2 py-1 text-xs bg-red-100 text-red-800 rounded">
                      {t('admin')}
                    </span>
                  )}
                </div>
              </div>
            </div>

            {/* 用户功能 */}
            <div className="py-2">
              <NavLink href="/profile" locale={locale}>
                 <FaUser /> 
                 {t('profile')}
              </NavLink>
             
             <NavLink href="/profile/submitted" locale={locale}>
                <FaList />
                {t('my_submissions')}
              </NavLink>

              <NavLink href="/profile/liked" locale={locale}>
                <FaHeart />
                {t('my_favorites')}
              </NavLink>

              <NavLink href="/submit" locale={locale}>
                  <FaPlus />
                  {t('submit_tool')}
              </NavLink>
            </div>

            {/* 管理员功能 */}
            {session.user?.role === 'admin' && (
              <div className="border-t py-2">
                <NavLink href="/admin" locale={locale}>
                  <FaCog />
                  {t('admin_panel')}
                </NavLink>
              </div>
            )}

            {/* 设置和登出 */}
            <div className="border-t py-2">
            <NavLink href="/settings" locale={locale}>
              <FaCog />
              {t('settings')}
            </NavLink>
            </div>
          </div>
        </>
      )}
    </div>
  );
}
