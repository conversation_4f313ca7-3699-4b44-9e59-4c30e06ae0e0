{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/workspace/aitools/aitools-website/src/components/LoadingSpinner.tsx"], "sourcesContent": ["'use client';\n\ninterface LoadingSpinnerProps {\n  size?: 'sm' | 'md' | 'lg';\n  className?: string;\n}\n\nexport default function LoadingSpinner({ size = 'md', className = '' }: LoadingSpinnerProps) {\n  const sizeClasses = {\n    sm: 'w-4 h-4',\n    md: 'w-8 h-8',\n    lg: 'w-12 h-12'\n  };\n\n  return (\n    <div className={`flex justify-center items-center ${className}`}>\n      <div className={`${sizeClasses[size]} border-4 border-gray-200 border-t-blue-600 rounded-full animate-spin`}></div>\n    </div>\n  );\n}\n"], "names": [], "mappings": ";;;;AAAA;;AAOe,SAAS,eAAe,EAAE,OAAO,IAAI,EAAE,YAAY,EAAE,EAAuB;IACzF,MAAM,cAAc;QAClB,IAAI;QACJ,IAAI;QACJ,IAAI;IACN;IAEA,qBACE,8OAAC;QAAI,WAAW,CAAC,iCAAiC,EAAE,WAAW;kBAC7D,cAAA,8OAAC;YAAI,WAAW,GAAG,WAAW,CAAC,KAAK,CAAC,qEAAqE,CAAC;;;;;;;;;;;AAGjH", "debugId": null}}, {"offset": {"line": 40, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/workspace/aitools/aitools-website/src/components/SuccessMessage.tsx"], "sourcesContent": ["'use client';\n\nimport { CheckCircle, X } from 'lucide-react';\n\ninterface SuccessMessageProps {\n  message: string;\n  onClose?: () => void;\n  className?: string;\n}\n\nexport default function SuccessMessage({ message, onClose, className = '' }: SuccessMessageProps) {\n  return (\n    <div className={`bg-green-50 border border-green-200 rounded-lg p-4 ${className}`}>\n      <div className=\"flex items-start\">\n        <CheckCircle className=\"w-5 h-5 text-green-500 mt-0.5 mr-3 flex-shrink-0\" />\n        <div className=\"flex-1\">\n          <p className=\"text-green-800 text-sm\">{message}</p>\n        </div>\n        {onClose && (\n          <button\n            onClick={onClose}\n            className=\"ml-3 text-green-400 hover:text-green-600 transition-colors\"\n          >\n            <X className=\"w-4 h-4\" />\n          </button>\n        )}\n      </div>\n    </div>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AAAA;AAFA;;;AAUe,SAAS,eAAe,EAAE,OAAO,EAAE,OAAO,EAAE,YAAY,EAAE,EAAuB;IAC9F,qBACE,8OAAC;QAAI,WAAW,CAAC,mDAAmD,EAAE,WAAW;kBAC/E,cAAA,8OAAC;YAAI,WAAU;;8BACb,8OAAC,2NAAA,CAAA,cAAW;oBAAC,WAAU;;;;;;8BACvB,8OAAC;oBAAI,WAAU;8BACb,cAAA,8OAAC;wBAAE,WAAU;kCAA0B;;;;;;;;;;;gBAExC,yBACC,8OAAC;oBACC,SAAS;oBACT,WAAU;8BAEV,cAAA,8OAAC,4LAAA,CAAA,IAAC;wBAAC,WAAU;;;;;;;;;;;;;;;;;;;;;;AAMzB", "debugId": null}}, {"offset": {"line": 110, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/workspace/aitools/aitools-website/src/constants/tags.ts"], "sourcesContent": ["// AI工具应用的预定义标签列表 - 精选最流行的50个标签\n// 标签键值，用于国际化\nexport const AVAILABLE_TAG_KEYS = [\n  // 核心AI功能\n  'ai_assistant', 'chatgpt', 'conversational_ai', 'smart_qa', 'language_model',\n\n  // 内容创作\n  'writing_assistant', 'content_generation', 'copywriting', 'blog_writing', 'marketing_copy',\n\n  // 图像处理\n  'image_generation', 'image_editing', 'ai_painting', 'avatar_generation', 'background_removal',\n\n  // 视频处理\n  'video_generation', 'video_editing', 'video_clipping', 'short_video_creation', 'video_subtitles',\n\n  // 音频处理\n  'speech_synthesis', 'speech_recognition', 'music_generation', 'speech_to_text', 'text_to_speech',\n\n  // 代码开发\n  'code_generation', 'code_completion', 'code_review', 'development_assistant', 'low_code_platform',\n\n  // 数据分析\n  'data_analysis', 'data_visualization', 'business_intelligence', 'machine_learning', 'deep_learning',\n\n  // 办公效率\n  'office_automation', 'document_processing', 'project_management', 'team_collaboration', 'note_taking',\n\n  // 设计工具\n  'ui_design', 'logo_design', 'web_design', 'graphic_design', 'prototype_design',\n\n  // 营销工具\n  'seo_optimization', 'social_media_marketing', 'email_marketing', 'content_marketing', 'market_analysis',\n\n  // 翻译工具\n  'machine_translation', 'real_time_translation', 'document_translation', 'voice_translation'\n];\n\n// 向后兼容的中文标签列表（已弃用，建议使用国际化版本）\nexport const AVAILABLE_TAGS = [\n  // 核心AI功能\n  'AI助手', 'ChatGPT', '对话AI', '智能问答', '语言模型',\n\n  // 内容创作\n  '写作助手', '内容生成', '文案创作', '博客写作', '营销文案',\n\n  // 图像处理\n  '图像生成', '图像编辑', 'AI绘画', '头像生成', '背景移除',\n\n  // 视频处理\n  '视频生成', '视频编辑', '视频剪辑', '短视频制作', '视频字幕',\n\n  // 音频处理\n  '语音合成', '语音识别', '音乐生成', '语音转文字', '文字转语音',\n\n  // 代码开发\n  '代码生成', '代码补全', '代码审查', '开发助手', '低代码平台',\n\n  // 数据分析\n  '数据分析', '数据可视化', '商业智能', '机器学习', '深度学习',\n\n  // 办公效率\n  '办公自动化', '文档处理', '项目管理', '团队协作', '笔记工具',\n\n  // 设计工具\n  'UI设计', 'Logo设计', '网页设计', '平面设计', '原型设计',\n\n  // 营销工具\n  'SEO优化', '社交媒体营销', '邮件营销', '内容营销', '市场分析',\n\n  // 翻译工具\n  '机器翻译', '实时翻译', '文档翻译', '语音翻译'\n];\n\n// 标签的最大选择数量\nexport const MAX_TAGS_COUNT = 3;\n\n// 按分类组织的标签键值（用于国际化）\nexport const TAG_KEYS_BY_CATEGORY = {\n  'core_ai': ['ai_assistant', 'chatgpt', 'conversational_ai', 'smart_qa', 'language_model'],\n  'content_creation': ['writing_assistant', 'content_generation', 'copywriting', 'blog_writing', 'marketing_copy'],\n  'image_processing': ['image_generation', 'image_editing', 'ai_painting', 'avatar_generation', 'background_removal'],\n  'video_processing': ['video_generation', 'video_editing', 'video_clipping', 'short_video_creation', 'video_subtitles'],\n  'audio_processing': ['speech_synthesis', 'speech_recognition', 'music_generation', 'speech_to_text', 'text_to_speech'],\n  'code_development': ['code_generation', 'code_completion', 'code_review', 'development_assistant', 'low_code_platform'],\n  'data_analysis': ['data_analysis', 'data_visualization', 'business_intelligence', 'machine_learning', 'deep_learning'],\n  'office_productivity': ['office_automation', 'document_processing', 'project_management', 'team_collaboration', 'note_taking'],\n  'design_tools': ['ui_design', 'logo_design', 'web_design', 'graphic_design', 'prototype_design'],\n  'marketing_tools': ['seo_optimization', 'social_media_marketing', 'email_marketing', 'content_marketing', 'market_analysis'],\n  'translation_tools': ['machine_translation', 'real_time_translation', 'document_translation', 'voice_translation']\n};\n\n// 向后兼容的中文分类标签（已弃用，建议使用国际化版本）\nexport const TAGS_BY_CATEGORY = {\n  '核心AI功能': ['AI助手', 'ChatGPT', '对话AI', '智能问答', '语言模型'],\n  '内容创作': ['写作助手', '内容生成', '文案创作', '博客写作', '营销文案'],\n  '图像处理': ['图像生成', '图像编辑', 'AI绘画', '头像生成', '背景移除'],\n  '视频处理': ['视频生成', '视频编辑', '视频剪辑', '短视频制作', '视频字幕'],\n  '音频处理': ['语音合成', '语音识别', '音乐生成', '语音转文字', '文字转语音'],\n  '代码开发': ['代码生成', '代码补全', '代码审查', '开发助手', '低代码平台'],\n  '数据分析': ['数据分析', '数据可视化', '商业智能', '机器学习', '深度学习'],\n  '办公效率': ['办公自动化', '文档处理', '项目管理', '团队协作', '笔记工具'],\n  '设计工具': ['UI设计', 'Logo设计', '网页设计', '平面设计', '原型设计'],\n  '营销工具': ['SEO优化', '社交媒体营销', '邮件营销', '内容营销', '市场分析'],\n  '翻译工具': ['机器翻译', '实时翻译', '文档翻译', '语音翻译']\n};\n"], "names": [], "mappings": "AAAA,+BAA+B;AAC/B,aAAa;;;;;;;;AACN,MAAM,qBAAqB;IAChC,SAAS;IACT;IAAgB;IAAW;IAAqB;IAAY;IAE5D,OAAO;IACP;IAAqB;IAAsB;IAAe;IAAgB;IAE1E,OAAO;IACP;IAAoB;IAAiB;IAAe;IAAqB;IAEzE,OAAO;IACP;IAAoB;IAAiB;IAAkB;IAAwB;IAE/E,OAAO;IACP;IAAoB;IAAsB;IAAoB;IAAkB;IAEhF,OAAO;IACP;IAAmB;IAAmB;IAAe;IAAyB;IAE9E,OAAO;IACP;IAAiB;IAAsB;IAAyB;IAAoB;IAEpF,OAAO;IACP;IAAqB;IAAuB;IAAsB;IAAsB;IAExF,OAAO;IACP;IAAa;IAAe;IAAc;IAAkB;IAE5D,OAAO;IACP;IAAoB;IAA0B;IAAmB;IAAqB;IAEtF,OAAO;IACP;IAAuB;IAAyB;IAAwB;CACzE;AAGM,MAAM,iBAAiB;IAC5B,SAAS;IACT;IAAQ;IAAW;IAAQ;IAAQ;IAEnC,OAAO;IACP;IAAQ;IAAQ;IAAQ;IAAQ;IAEhC,OAAO;IACP;IAAQ;IAAQ;IAAQ;IAAQ;IAEhC,OAAO;IACP;IAAQ;IAAQ;IAAQ;IAAS;IAEjC,OAAO;IACP;IAAQ;IAAQ;IAAQ;IAAS;IAEjC,OAAO;IACP;IAAQ;IAAQ;IAAQ;IAAQ;IAEhC,OAAO;IACP;IAAQ;IAAS;IAAQ;IAAQ;IAEjC,OAAO;IACP;IAAS;IAAQ;IAAQ;IAAQ;IAEjC,OAAO;IACP;IAAQ;IAAU;IAAQ;IAAQ;IAElC,OAAO;IACP;IAAS;IAAU;IAAQ;IAAQ;IAEnC,OAAO;IACP;IAAQ;IAAQ;IAAQ;CACzB;AAGM,MAAM,iBAAiB;AAGvB,MAAM,uBAAuB;IAClC,WAAW;QAAC;QAAgB;QAAW;QAAqB;QAAY;KAAiB;IACzF,oBAAoB;QAAC;QAAqB;QAAsB;QAAe;QAAgB;KAAiB;IAChH,oBAAoB;QAAC;QAAoB;QAAiB;QAAe;QAAqB;KAAqB;IACnH,oBAAoB;QAAC;QAAoB;QAAiB;QAAkB;QAAwB;KAAkB;IACtH,oBAAoB;QAAC;QAAoB;QAAsB;QAAoB;QAAkB;KAAiB;IACtH,oBAAoB;QAAC;QAAmB;QAAmB;QAAe;QAAyB;KAAoB;IACvH,iBAAiB;QAAC;QAAiB;QAAsB;QAAyB;QAAoB;KAAgB;IACtH,uBAAuB;QAAC;QAAqB;QAAuB;QAAsB;QAAsB;KAAc;IAC9H,gBAAgB;QAAC;QAAa;QAAe;QAAc;QAAkB;KAAmB;IAChG,mBAAmB;QAAC;QAAoB;QAA0B;QAAmB;QAAqB;KAAkB;IAC5H,qBAAqB;QAAC;QAAuB;QAAyB;QAAwB;KAAoB;AACpH;AAGO,MAAM,mBAAmB;IAC9B,UAAU;QAAC;QAAQ;QAAW;QAAQ;QAAQ;KAAO;IACrD,QAAQ;QAAC;QAAQ;QAAQ;QAAQ;QAAQ;KAAO;IAChD,QAAQ;QAAC;QAAQ;QAAQ;QAAQ;QAAQ;KAAO;IAChD,QAAQ;QAAC;QAAQ;QAAQ;QAAQ;QAAS;KAAO;IACjD,QAAQ;QAAC;QAAQ;QAAQ;QAAQ;QAAS;KAAQ;IAClD,QAAQ;QAAC;QAAQ;QAAQ;QAAQ;QAAQ;KAAQ;IACjD,QAAQ;QAAC;QAAQ;QAAS;QAAQ;QAAQ;KAAO;IACjD,QAAQ;QAAC;QAAS;QAAQ;QAAQ;QAAQ;KAAO;IACjD,QAAQ;QAAC;QAAQ;QAAU;QAAQ;QAAQ;KAAO;IAClD,QAAQ;QAAC;QAAS;QAAU;QAAQ;QAAQ;KAAO;IACnD,QAAQ;QAAC;QAAQ;QAAQ;QAAQ;KAAO;AAC1C", "debugId": null}}, {"offset": {"line": 416, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/workspace/aitools/aitools-website/src/constants/tags-i18n.ts"], "sourcesContent": ["// 国际化标签配置文件\n// 支持多语言的AI工具标签配置\n\nimport { useTranslations } from 'next-intl';\nimport { getTranslations } from 'next-intl/server';\nimport { AVAILABLE_TAG_KEYS, TAG_KEYS_BY_CATEGORY } from './tags';\n\nexport interface TagOption {\n  key: string;\n  label: string;\n}\n\nexport interface TagCategory {\n  key: string;\n  name: string;\n  tags: TagOption[];\n}\n\n// 客户端钩子：获取国际化的标签选项\nexport function useTagOptions(): TagOption[] {\n  const t = useTranslations('tags');\n  \n  return AVAILABLE_TAG_KEYS.map(key => ({\n    key,\n    label: t(key)\n  }));\n}\n\n// 客户端钩子：获取按分类组织的国际化标签\nexport function useTagsByCategory(): TagCategory[] {\n  const t = useTranslations('tags');\n  const categoryT = useTranslations('tag_categories');\n  \n  return Object.entries(TAG_KEYS_BY_CATEGORY).map(([categoryKey, tagKeys]) => ({\n    key: categoryKey,\n    name: categoryT(categoryKey),\n    tags: tagKeys.map(tagKey => ({\n      key: tagKey,\n      label: t(tagKey)\n    }))\n  }));\n}\n\n// 客户端钩子：获取单个标签的翻译\nexport function useTagLabel(tagKey: string): string {\n  const t = useTranslations('tags');\n  return t(tagKey) || tagKey;\n}\n\n// 客户端钩子：获取标签分类名称\nexport function useTagCategoryName(categoryKey: string): string {\n  const t = useTranslations('tag_categories');\n  return t(categoryKey) || categoryKey;\n}\n\n// 获取所有可用的标签键值（用于验证）\nexport function getAvailableTagKeys(): string[] {\n  return AVAILABLE_TAG_KEYS;\n}\n\n// 获取所有可用的标签分类键值（用于验证）\nexport function getAvailableTagCategoryKeys(): string[] {\n  return Object.keys(TAG_KEYS_BY_CATEGORY);\n}\n\n// 服务器端函数：获取国际化的标签选项\nexport async function getTagOptions(locale?: string): Promise<TagOption[]> {\n  const t = await getTranslations({ locale: locale || 'en', namespace: 'tags' });\n\n  return AVAILABLE_TAG_KEYS.map(key => ({\n    key,\n    label: t(key)\n  }));\n}\n\n// 服务器端函数：获取按分类组织的国际化标签\nexport async function getTagsByCategory(locale?: string): Promise<TagCategory[]> {\n  const t = await getTranslations({ locale: locale || 'en', namespace: 'tags' });\n  const categoryT = await getTranslations({ locale: locale || 'en', namespace: 'tag_categories' });\n\n  return Object.entries(TAG_KEYS_BY_CATEGORY).map(([categoryKey, tagKeys]) => ({\n    key: categoryKey,\n    name: categoryT(categoryKey),\n    tags: tagKeys.map(tagKey => ({\n      key: tagKey,\n      label: t(tagKey)\n    }))\n  }));\n}\n\n// 服务器端函数：获取单个标签的翻译\nexport async function getTagLabel(tagKey: string, locale?: string): Promise<string> {\n  const t = await getTranslations({ locale: locale || 'en', namespace: 'tags' });\n  return t(tagKey) || tagKey;\n}\n\n// 服务器端函数：获取标签分类名称\nexport async function getTagCategoryName(categoryKey: string, locale?: string): Promise<string> {\n  const t = await getTranslations({ locale: locale || 'en', namespace: 'tag_categories' });\n  return t(categoryKey) || categoryKey;\n}\n"], "names": [], "mappings": "AAAA,YAAY;AACZ,iBAAiB;;;;;;;;;;;;;AAEjB;AACA;AACA;;;;AAcO,SAAS;IACd,MAAM,IAAI,CAAA,GAAA,sMAAA,CAAA,kBAAe,AAAD,EAAE;IAE1B,OAAO,wHAAA,CAAA,qBAAkB,CAAC,GAAG,CAAC,CAAA,MAAO,CAAC;YACpC;YACA,OAAO,EAAE;QACX,CAAC;AACH;AAGO,SAAS;IACd,MAAM,IAAI,CAAA,GAAA,sMAAA,CAAA,kBAAe,AAAD,EAAE;IAC1B,MAAM,YAAY,CAAA,GAAA,sMAAA,CAAA,kBAAe,AAAD,EAAE;IAElC,OAAO,OAAO,OAAO,CAAC,wHAAA,CAAA,uBAAoB,EAAE,GAAG,CAAC,CAAC,CAAC,aAAa,QAAQ,GAAK,CAAC;YAC3E,KAAK;YACL,MAAM,UAAU;YAChB,MAAM,QAAQ,GAAG,CAAC,CAAA,SAAU,CAAC;oBAC3B,KAAK;oBACL,OAAO,EAAE;gBACX,CAAC;QACH,CAAC;AACH;AAGO,SAAS,YAAY,MAAc;IACxC,MAAM,IAAI,CAAA,GAAA,sMAAA,CAAA,kBAAe,AAAD,EAAE;IAC1B,OAAO,EAAE,WAAW;AACtB;AAGO,SAAS,mBAAmB,WAAmB;IACpD,MAAM,IAAI,CAAA,GAAA,sMAAA,CAAA,kBAAe,AAAD,EAAE;IAC1B,OAAO,EAAE,gBAAgB;AAC3B;AAGO,SAAS;IACd,OAAO,wHAAA,CAAA,qBAAkB;AAC3B;AAGO,SAAS;IACd,OAAO,OAAO,IAAI,CAAC,wHAAA,CAAA,uBAAoB;AACzC;AAGO,eAAe,cAAc,MAAe;IACjD,MAAM,IAAI,MAAM,CAAA,GAAA,gMAAA,CAAA,kBAAe,AAAD,EAAE;QAAE,QAAQ,UAAU;QAAM,WAAW;IAAO;IAE5E,OAAO,wHAAA,CAAA,qBAAkB,CAAC,GAAG,CAAC,CAAA,MAAO,CAAC;YACpC;YACA,OAAO,EAAE;QACX,CAAC;AACH;AAGO,eAAe,kBAAkB,MAAe;IACrD,MAAM,IAAI,MAAM,CAAA,GAAA,gMAAA,CAAA,kBAAe,AAAD,EAAE;QAAE,QAAQ,UAAU;QAAM,WAAW;IAAO;IAC5E,MAAM,YAAY,MAAM,CAAA,GAAA,gMAAA,CAAA,kBAAe,AAAD,EAAE;QAAE,QAAQ,UAAU;QAAM,WAAW;IAAiB;IAE9F,OAAO,OAAO,OAAO,CAAC,wHAAA,CAAA,uBAAoB,EAAE,GAAG,CAAC,CAAC,CAAC,aAAa,QAAQ,GAAK,CAAC;YAC3E,KAAK;YACL,MAAM,UAAU;YAChB,MAAM,QAAQ,GAAG,CAAC,CAAA,SAAU,CAAC;oBAC3B,KAAK;oBACL,OAAO,EAAE;gBACX,CAAC;QACH,CAAC;AACH;AAGO,eAAe,YAAY,MAAc,EAAE,MAAe;IAC/D,MAAM,IAAI,MAAM,CAAA,GAAA,gMAAA,CAAA,kBAAe,AAAD,EAAE;QAAE,QAAQ,UAAU;QAAM,WAAW;IAAO;IAC5E,OAAO,EAAE,WAAW;AACtB;AAGO,eAAe,mBAAmB,WAAmB,EAAE,MAAe;IAC3E,MAAM,IAAI,MAAM,CAAA,GAAA,gMAAA,CAAA,kBAAe,AAAD,EAAE;QAAE,QAAQ,UAAU;QAAM,WAAW;IAAiB;IACtF,OAAO,EAAE,gBAAgB;AAC3B", "debugId": null}}, {"offset": {"line": 517, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/workspace/aitools/aitools-website/src/components/TagSelector.tsx"], "sourcesContent": ["'use client';\n\nimport React, { useState } from 'react';\nimport { usePathname } from '@/i18n/routing';\nimport { useTranslations } from 'next-intl';\nimport { Tag, Search, X } from 'lucide-react';\nimport { MAX_TAGS_COUNT } from '@/constants/tags';\nimport { useTagOptions } from '@/constants/tags-i18n';\nimport { Locale } from '@/i18n/config';\n\ninterface TagSelectorProps {\n  selectedTags: string[];\n  onTagsChange: (tags: string[]) => void;\n  maxTags?: number;\n  placeholder?: string;\n}\n\nexport default function TagSelector({\n  selectedTags,\n  onTagsChange,\n  maxTags = MAX_TAGS_COUNT,\n  placeholder\n}: TagSelectorProps) {\n  const [searchTerm, setSearchTerm] = useState('');\n  const [isOpen, setIsOpen] = useState(false);\n\n  const pathname = usePathname();\n  const t = useTranslations('common');\n  const tagOptions = useTagOptions();\n\n  // Extract current locale from pathname\n  const currentLocale = (pathname?.startsWith('/en') ? 'en' : 'zh') as Locale;\n\n  const toggleTag = (tagKey: string) => {\n    if (selectedTags.includes(tagKey)) {\n      onTagsChange(selectedTags.filter(t => t !== tagKey));\n    } else if (selectedTags.length < maxTags) {\n      onTagsChange([...selectedTags, tagKey]);\n    }\n  };\n\n  const removeTag = (tagKey: string) => {\n    onTagsChange(selectedTags.filter(t => t !== tagKey));\n  };\n\n  // 过滤标签：根据搜索词过滤，并排除已选择的标签\n  const filteredTags = tagOptions.filter(tag =>\n    tag.label.toLowerCase().includes(searchTerm.toLowerCase()) &&\n    !selectedTags.includes(tag.key)\n  );\n\n  // 获取已选择标签的显示文本\n  const getSelectedTagLabel = (tagKey: string) => {\n    const tagOption = tagOptions.find(tag => tag.key === tagKey);\n    return tagOption ? tagOption.label : tagKey;\n  };\n\n  return (\n    <div className=\"space-y-4\">\n      {/* 标题和计数器 */}\n      <div className=\"flex items-center justify-between\">\n        <h3 className=\"text-lg font-medium text-gray-900\">{t('select_tags')}</h3>\n        <span className=\"text-sm text-gray-500\">\n          {t('selected_count', { count: selectedTags.length, max: maxTags })}\n        </span>\n      </div>\n\n      {/* 已选择的标签 */}\n      {selectedTags.length > 0 && (\n        <div className=\"space-y-2\">\n          <h4 className=\"text-sm font-medium text-gray-700\">{t('selected_tags')}</h4>\n          <div className=\"flex flex-wrap gap-2\">\n            {selectedTags.map((tagKey) => (\n              <span\n                key={tagKey}\n                className=\"inline-flex items-center px-3 py-1 rounded-md text-sm font-medium bg-blue-100 text-blue-800\"\n              >\n                {getSelectedTagLabel(tagKey)}\n                <button\n                  type=\"button\"\n                  onClick={() => removeTag(tagKey)}\n                  className=\"ml-2 text-blue-600 hover:text-blue-800\"\n                >\n                  <X className=\"h-3 w-3\" />\n                </button>\n              </span>\n            ))}\n          </div>\n        </div>\n      )}\n\n      {/* 标签选择器 */}\n      <div className=\"space-y-3\">\n        <div className=\"relative\">\n          <label className=\"block text-sm font-medium text-gray-700 mb-2\">\n            {t('select_tags_max', { max: maxTags })}\n          </label>\n\n          {/* 搜索框 */}\n          <div className=\"relative mb-3\">\n            <input\n              type=\"text\"\n              placeholder={placeholder || t('search_tags')}\n              value={searchTerm}\n              onChange={(e) => setSearchTerm(e.target.value)}\n              onFocus={() => setIsOpen(true)}\n              className=\"w-full pl-10 pr-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent\"\n            />\n            <Search className=\"absolute left-3 top-2.5 h-4 w-4 text-gray-400\" />\n          </div>\n\n          {/* 标签选择下拉框 */}\n          {(isOpen || searchTerm) && (\n            <div className=\"relative\">\n              <div className=\"absolute z-10 w-full bg-white border border-gray-300 rounded-lg shadow-lg max-h-60 overflow-y-auto\">\n                {filteredTags.length > 0 ? (\n                  <div className=\"p-2\">\n                    <div className=\"grid grid-cols-1 gap-1\">\n                      {filteredTags.map((tag) => {\n                        const isDisabled = selectedTags.length >= maxTags;\n\n                        return (\n                          <button\n                            key={tag.key}\n                            type=\"button\"\n                            onClick={() => {\n                              toggleTag(tag.key);\n                              setSearchTerm('');\n                              setIsOpen(false);\n                            }}\n                            disabled={isDisabled}\n                            className={`\n                              w-full px-3 py-2 text-sm text-left rounded-md transition-colors\n                              ${isDisabled\n                                ? 'bg-gray-100 text-gray-400 cursor-not-allowed'\n                                : 'hover:bg-blue-50 text-gray-700'\n                              }\n                            `}\n                          >\n                            <div className=\"flex items-center\">\n                              <Tag className=\"h-3 w-3 mr-2 text-gray-400\" />\n                              {tag.label}\n                            </div>\n                          </button>\n                        );\n                      })}\n                    </div>\n                    {filteredTags.length > 50 && (\n                      <p className=\"text-xs text-gray-500 mt-2 px-3\">\n                        {t('found_tags', { count: filteredTags.length })}\n                      </p>\n                    )}\n                  </div>\n                ) : (\n                  <div className=\"p-4 text-center text-gray-500 text-sm\">\n                    {searchTerm ? t('no_tags_found') : t('start_typing')}\n                  </div>\n                )}\n              </div>\n            </div>\n          )}\n        </div>\n      </div>\n\n      {/* 点击外部关闭下拉框 */}\n      {(isOpen || searchTerm) && (\n        <div\n          className=\"fixed inset-0 z-0\"\n          onClick={() => {\n            setIsOpen(false);\n            setSearchTerm('');\n          }}\n        />\n      )}\n\n      {/* 提示信息 */}\n      {selectedTags.length >= maxTags && (\n        <p className=\"text-sm text-amber-600\">\n          {t('max_tags_limit', { max: maxTags })}\n        </p>\n      )}\n    </div>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AAAA;AAAA;AACA;AACA;AAPA;;;;;;;;AAiBe,SAAS,YAAY,EAClC,YAAY,EACZ,YAAY,EACZ,UAAU,wHAAA,CAAA,iBAAc,EACxB,WAAW,EACM;IACjB,MAAM,CAAC,YAAY,cAAc,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAC7C,MAAM,CAAC,QAAQ,UAAU,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAErC,MAAM,WAAW,CAAA,GAAA,sHAAA,CAAA,cAAW,AAAD;IAC3B,MAAM,IAAI,CAAA,GAAA,sMAAA,CAAA,kBAAe,AAAD,EAAE;IAC1B,MAAM,aAAa,CAAA,GAAA,gIAAA,CAAA,gBAAa,AAAD;IAE/B,uCAAuC;IACvC,MAAM,gBAAiB,UAAU,WAAW,SAAS,OAAO;IAE5D,MAAM,YAAY,CAAC;QACjB,IAAI,aAAa,QAAQ,CAAC,SAAS;YACjC,aAAa,aAAa,MAAM,CAAC,CAAA,IAAK,MAAM;QAC9C,OAAO,IAAI,aAAa,MAAM,GAAG,SAAS;YACxC,aAAa;mBAAI;gBAAc;aAAO;QACxC;IACF;IAEA,MAAM,YAAY,CAAC;QACjB,aAAa,aAAa,MAAM,CAAC,CAAA,IAAK,MAAM;IAC9C;IAEA,yBAAyB;IACzB,MAAM,eAAe,WAAW,MAAM,CAAC,CAAA,MACrC,IAAI,KAAK,CAAC,WAAW,GAAG,QAAQ,CAAC,WAAW,WAAW,OACvD,CAAC,aAAa,QAAQ,CAAC,IAAI,GAAG;IAGhC,eAAe;IACf,MAAM,sBAAsB,CAAC;QAC3B,MAAM,YAAY,WAAW,IAAI,CAAC,CAAA,MAAO,IAAI,GAAG,KAAK;QACrD,OAAO,YAAY,UAAU,KAAK,GAAG;IACvC;IAEA,qBACE,8OAAC;QAAI,WAAU;;0BAEb,8OAAC;gBAAI,WAAU;;kCACb,8OAAC;wBAAG,WAAU;kCAAqC,EAAE;;;;;;kCACrD,8OAAC;wBAAK,WAAU;kCACb,EAAE,kBAAkB;4BAAE,OAAO,aAAa,MAAM;4BAAE,KAAK;wBAAQ;;;;;;;;;;;;YAKnE,aAAa,MAAM,GAAG,mBACrB,8OAAC;gBAAI,WAAU;;kCACb,8OAAC;wBAAG,WAAU;kCAAqC,EAAE;;;;;;kCACrD,8OAAC;wBAAI,WAAU;kCACZ,aAAa,GAAG,CAAC,CAAC,uBACjB,8OAAC;gCAEC,WAAU;;oCAET,oBAAoB;kDACrB,8OAAC;wCACC,MAAK;wCACL,SAAS,IAAM,UAAU;wCACzB,WAAU;kDAEV,cAAA,8OAAC,4LAAA,CAAA,IAAC;4CAAC,WAAU;;;;;;;;;;;;+BATV;;;;;;;;;;;;;;;;0BAkBf,8OAAC;gBAAI,WAAU;0BACb,cAAA,8OAAC;oBAAI,WAAU;;sCACb,8OAAC;4BAAM,WAAU;sCACd,EAAE,mBAAmB;gCAAE,KAAK;4BAAQ;;;;;;sCAIvC,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCACC,MAAK;oCACL,aAAa,eAAe,EAAE;oCAC9B,OAAO;oCACP,UAAU,CAAC,IAAM,cAAc,EAAE,MAAM,CAAC,KAAK;oCAC7C,SAAS,IAAM,UAAU;oCACzB,WAAU;;;;;;8CAEZ,8OAAC,sMAAA,CAAA,SAAM;oCAAC,WAAU;;;;;;;;;;;;wBAInB,CAAC,UAAU,UAAU,mBACpB,8OAAC;4BAAI,WAAU;sCACb,cAAA,8OAAC;gCAAI,WAAU;0CACZ,aAAa,MAAM,GAAG,kBACrB,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CAAI,WAAU;sDACZ,aAAa,GAAG,CAAC,CAAC;gDACjB,MAAM,aAAa,aAAa,MAAM,IAAI;gDAE1C,qBACE,8OAAC;oDAEC,MAAK;oDACL,SAAS;wDACP,UAAU,IAAI,GAAG;wDACjB,cAAc;wDACd,UAAU;oDACZ;oDACA,UAAU;oDACV,WAAW,CAAC;;8BAEV,EAAE,aACE,iDACA,iCACH;4BACH,CAAC;8DAED,cAAA,8OAAC;wDAAI,WAAU;;0EACb,8OAAC,gMAAA,CAAA,MAAG;gEAAC,WAAU;;;;;;4DACd,IAAI,KAAK;;;;;;;mDAlBP,IAAI,GAAG;;;;;4CAsBlB;;;;;;wCAED,aAAa,MAAM,GAAG,oBACrB,8OAAC;4CAAE,WAAU;sDACV,EAAE,cAAc;gDAAE,OAAO,aAAa,MAAM;4CAAC;;;;;;;;;;;yDAKpD,8OAAC;oCAAI,WAAU;8CACZ,aAAa,EAAE,mBAAmB,EAAE;;;;;;;;;;;;;;;;;;;;;;;;;;;YAUlD,CAAC,UAAU,UAAU,mBACpB,8OAAC;gBACC,WAAU;gBACV,SAAS;oBACP,UAAU;oBACV,cAAc;gBAChB;;;;;;YAKH,aAAa,MAAM,IAAI,yBACtB,8OAAC;gBAAE,WAAU;0BACV,EAAE,kBAAkB;oBAAE,KAAK;gBAAQ;;;;;;;;;;;;AAK9C", "debugId": null}}]}