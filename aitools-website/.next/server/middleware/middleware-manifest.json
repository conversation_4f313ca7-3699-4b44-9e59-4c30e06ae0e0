{"sorted_middleware": [], "middleware": {"/": {"files": ["server/edge/chunks/_915e9f02._.js", "server/edge/chunks/node_modules_@formatjs_intl-localematcher_lib_f4580406._.js", "server/edge/chunks/node_modules_4ac6bddb._.js", "server/edge/chunks/[root-of-the-server]__e8693e40._.js", "server/edge/chunks/edge-wrapper_ff61254e.js"], "name": "middleware", "page": "/", "matchers": [{"regexp": "/:nextData(_next/data/[^/]{1,})?/((?!api|_next/static|_next/image|favicon.ico|robots.txt|sitemap.xml|uploads).*){(\\\\.json)}?", "originalSource": "/((?!api|_next/static|_next/image|favicon.ico|robots.txt|sitemap.xml|uploads).*)"}], "wasm": [], "assets": [], "env": {"__NEXT_BUILD_ID": "development", "NEXT_SERVER_ACTIONS_ENCRYPTION_KEY": "pbGO3VwpkSvUolI25fpFPUUkncDK/RUErZt9oTegUAc=", "__NEXT_PREVIEW_MODE_ID": "186acb5c10d141b65e8c7743ecf0fe8b", "__NEXT_PREVIEW_MODE_ENCRYPTION_KEY": "fbc2fd059bc1edf6845f77a3079f9e1190c822a6d532b9fa6f9cade1f29b1737", "__NEXT_PREVIEW_MODE_SIGNING_KEY": "9ff213308845db7f5ef9615b26791526a9aed3d2c673fc1e61ecbd81be32d15a"}}}, "instrumentation": null, "functions": {}}