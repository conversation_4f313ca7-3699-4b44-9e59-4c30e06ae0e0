{"sorted_middleware": [], "middleware": {"/": {"files": ["server/edge/chunks/_256bb82d._.js", "server/edge/chunks/node_modules_@formatjs_intl-localematcher_lib_f4580406._.js", "server/edge/chunks/node_modules_4ac6bddb._.js", "server/edge/chunks/[root-of-the-server]__abf7a29f._.js", "server/edge/chunks/edge-wrapper_80fb5899.js"], "name": "middleware", "page": "/", "matchers": [{"regexp": "/:nextData(_next/data/[^/]{1,})?/((?!api|_next/static|_next/image|favicon.ico|robots.txt|sitemap.xml|uploads).*){(\\\\.json)}?", "originalSource": "/((?!api|_next/static|_next/image|favicon.ico|robots.txt|sitemap.xml|uploads).*)"}], "wasm": [], "assets": [], "env": {"__NEXT_BUILD_ID": "development", "NEXT_SERVER_ACTIONS_ENCRYPTION_KEY": "pbGO3VwpkSvUolI25fpFPUUkncDK/RUErZt9oTegUAc=", "__NEXT_PREVIEW_MODE_ID": "c3cae0fa5792c52416e84fa266546718", "__NEXT_PREVIEW_MODE_ENCRYPTION_KEY": "c3494022c9085c3b72bdaa1fa4754e3babeb4d61f3f3e31382852210347ac913", "__NEXT_PREVIEW_MODE_SIGNING_KEY": "4bdf9f5e0b9d853d7db44c2b87abc8be08968215cebe11e53cb383c669f4ab9c"}}}, "instrumentation": null, "functions": {}}