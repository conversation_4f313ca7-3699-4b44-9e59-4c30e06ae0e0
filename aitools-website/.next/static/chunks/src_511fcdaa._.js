(globalThis.TURBOPACK = globalThis.TURBOPACK || []).push([typeof document === "object" ? document.currentScript : undefined, {

"[project]/src/components/LoadingSpinner.tsx [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname, k: __turbopack_refresh__, m: module } = __turbopack_context__;
{
__turbopack_context__.s({
    "default": (()=>LoadingSpinner)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/compiled/react/jsx-dev-runtime.js [app-client] (ecmascript)");
'use client';
;
function LoadingSpinner({ size = 'md', className = '' }) {
    const sizeClasses = {
        sm: 'w-4 h-4',
        md: 'w-8 h-8',
        lg: 'w-12 h-12'
    };
    return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
        className: `flex justify-center items-center ${className}`,
        children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
            className: `${sizeClasses[size]} border-4 border-gray-200 border-t-blue-600 rounded-full animate-spin`
        }, void 0, false, {
            fileName: "[project]/src/components/LoadingSpinner.tsx",
            lineNumber: 17,
            columnNumber: 7
        }, this)
    }, void 0, false, {
        fileName: "[project]/src/components/LoadingSpinner.tsx",
        lineNumber: 16,
        columnNumber: 5
    }, this);
}
_c = LoadingSpinner;
var _c;
__turbopack_context__.k.register(_c, "LoadingSpinner");
if (typeof globalThis.$RefreshHelpers$ === 'object' && globalThis.$RefreshHelpers !== null) {
    __turbopack_context__.k.registerExports(module, globalThis.$RefreshHelpers$);
}
}}),
"[project]/src/components/SuccessMessage.tsx [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname, k: __turbopack_refresh__, m: module } = __turbopack_context__;
{
__turbopack_context__.s({
    "default": (()=>SuccessMessage)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/compiled/react/jsx-dev-runtime.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$circle$2d$check$2d$big$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__CheckCircle$3e$__ = __turbopack_context__.i("[project]/node_modules/lucide-react/dist/esm/icons/circle-check-big.js [app-client] (ecmascript) <export default as CheckCircle>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$x$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__X$3e$__ = __turbopack_context__.i("[project]/node_modules/lucide-react/dist/esm/icons/x.js [app-client] (ecmascript) <export default as X>");
'use client';
;
;
function SuccessMessage({ message, onClose, className = '' }) {
    return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
        className: `bg-green-50 border border-green-200 rounded-lg p-4 ${className}`,
        children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
            className: "flex items-start",
            children: [
                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$circle$2d$check$2d$big$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__CheckCircle$3e$__["CheckCircle"], {
                    className: "w-5 h-5 text-green-500 mt-0.5 mr-3 flex-shrink-0"
                }, void 0, false, {
                    fileName: "[project]/src/components/SuccessMessage.tsx",
                    lineNumber: 15,
                    columnNumber: 9
                }, this),
                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                    className: "flex-1",
                    children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("p", {
                        className: "text-green-800 text-sm",
                        children: message
                    }, void 0, false, {
                        fileName: "[project]/src/components/SuccessMessage.tsx",
                        lineNumber: 17,
                        columnNumber: 11
                    }, this)
                }, void 0, false, {
                    fileName: "[project]/src/components/SuccessMessage.tsx",
                    lineNumber: 16,
                    columnNumber: 9
                }, this),
                onClose && /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("button", {
                    onClick: onClose,
                    className: "ml-3 text-green-400 hover:text-green-600 transition-colors",
                    children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$x$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__X$3e$__["X"], {
                        className: "w-4 h-4"
                    }, void 0, false, {
                        fileName: "[project]/src/components/SuccessMessage.tsx",
                        lineNumber: 24,
                        columnNumber: 13
                    }, this)
                }, void 0, false, {
                    fileName: "[project]/src/components/SuccessMessage.tsx",
                    lineNumber: 20,
                    columnNumber: 11
                }, this)
            ]
        }, void 0, true, {
            fileName: "[project]/src/components/SuccessMessage.tsx",
            lineNumber: 14,
            columnNumber: 7
        }, this)
    }, void 0, false, {
        fileName: "[project]/src/components/SuccessMessage.tsx",
        lineNumber: 13,
        columnNumber: 5
    }, this);
}
_c = SuccessMessage;
var _c;
__turbopack_context__.k.register(_c, "SuccessMessage");
if (typeof globalThis.$RefreshHelpers$ === 'object' && globalThis.$RefreshHelpers !== null) {
    __turbopack_context__.k.registerExports(module, globalThis.$RefreshHelpers$);
}
}}),
"[project]/src/constants/tags.ts [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname, k: __turbopack_refresh__, m: module } = __turbopack_context__;
{
// AI工具应用的预定义标签列表 - 精选最流行的50个标签
// 标签键值，用于国际化
__turbopack_context__.s({
    "AVAILABLE_TAGS": (()=>AVAILABLE_TAGS),
    "AVAILABLE_TAG_KEYS": (()=>AVAILABLE_TAG_KEYS),
    "MAX_TAGS_COUNT": (()=>MAX_TAGS_COUNT),
    "TAGS_BY_CATEGORY": (()=>TAGS_BY_CATEGORY),
    "TAG_KEYS_BY_CATEGORY": (()=>TAG_KEYS_BY_CATEGORY)
});
const AVAILABLE_TAG_KEYS = [
    // 核心AI功能
    'ai_assistant',
    'chatgpt',
    'conversational_ai',
    'smart_qa',
    'language_model',
    // 内容创作
    'writing_assistant',
    'content_generation',
    'copywriting',
    'blog_writing',
    'marketing_copy',
    // 图像处理
    'image_generation',
    'image_editing',
    'ai_painting',
    'avatar_generation',
    'background_removal',
    // 视频处理
    'video_generation',
    'video_editing',
    'video_clipping',
    'short_video_creation',
    'video_subtitles',
    // 音频处理
    'speech_synthesis',
    'speech_recognition',
    'music_generation',
    'speech_to_text',
    'text_to_speech',
    // 代码开发
    'code_generation',
    'code_completion',
    'code_review',
    'development_assistant',
    'low_code_platform',
    // 数据分析
    'data_analysis',
    'data_visualization',
    'business_intelligence',
    'machine_learning',
    'deep_learning',
    // 办公效率
    'office_automation',
    'document_processing',
    'project_management',
    'team_collaboration',
    'note_taking',
    // 设计工具
    'ui_design',
    'logo_design',
    'web_design',
    'graphic_design',
    'prototype_design',
    // 营销工具
    'seo_optimization',
    'social_media_marketing',
    'email_marketing',
    'content_marketing',
    'market_analysis',
    // 翻译工具
    'machine_translation',
    'real_time_translation',
    'document_translation',
    'voice_translation'
];
const AVAILABLE_TAGS = [
    // 核心AI功能
    'AI助手',
    'ChatGPT',
    '对话AI',
    '智能问答',
    '语言模型',
    // 内容创作
    '写作助手',
    '内容生成',
    '文案创作',
    '博客写作',
    '营销文案',
    // 图像处理
    '图像生成',
    '图像编辑',
    'AI绘画',
    '头像生成',
    '背景移除',
    // 视频处理
    '视频生成',
    '视频编辑',
    '视频剪辑',
    '短视频制作',
    '视频字幕',
    // 音频处理
    '语音合成',
    '语音识别',
    '音乐生成',
    '语音转文字',
    '文字转语音',
    // 代码开发
    '代码生成',
    '代码补全',
    '代码审查',
    '开发助手',
    '低代码平台',
    // 数据分析
    '数据分析',
    '数据可视化',
    '商业智能',
    '机器学习',
    '深度学习',
    // 办公效率
    '办公自动化',
    '文档处理',
    '项目管理',
    '团队协作',
    '笔记工具',
    // 设计工具
    'UI设计',
    'Logo设计',
    '网页设计',
    '平面设计',
    '原型设计',
    // 营销工具
    'SEO优化',
    '社交媒体营销',
    '邮件营销',
    '内容营销',
    '市场分析',
    // 翻译工具
    '机器翻译',
    '实时翻译',
    '文档翻译',
    '语音翻译'
];
const MAX_TAGS_COUNT = 3;
const TAG_KEYS_BY_CATEGORY = {
    'core_ai': [
        'ai_assistant',
        'chatgpt',
        'conversational_ai',
        'smart_qa',
        'language_model'
    ],
    'content_creation': [
        'writing_assistant',
        'content_generation',
        'copywriting',
        'blog_writing',
        'marketing_copy'
    ],
    'image_processing': [
        'image_generation',
        'image_editing',
        'ai_painting',
        'avatar_generation',
        'background_removal'
    ],
    'video_processing': [
        'video_generation',
        'video_editing',
        'video_clipping',
        'short_video_creation',
        'video_subtitles'
    ],
    'audio_processing': [
        'speech_synthesis',
        'speech_recognition',
        'music_generation',
        'speech_to_text',
        'text_to_speech'
    ],
    'code_development': [
        'code_generation',
        'code_completion',
        'code_review',
        'development_assistant',
        'low_code_platform'
    ],
    'data_analysis': [
        'data_analysis',
        'data_visualization',
        'business_intelligence',
        'machine_learning',
        'deep_learning'
    ],
    'office_productivity': [
        'office_automation',
        'document_processing',
        'project_management',
        'team_collaboration',
        'note_taking'
    ],
    'design_tools': [
        'ui_design',
        'logo_design',
        'web_design',
        'graphic_design',
        'prototype_design'
    ],
    'marketing_tools': [
        'seo_optimization',
        'social_media_marketing',
        'email_marketing',
        'content_marketing',
        'market_analysis'
    ],
    'translation_tools': [
        'machine_translation',
        'real_time_translation',
        'document_translation',
        'voice_translation'
    ]
};
const TAGS_BY_CATEGORY = {
    '核心AI功能': [
        'AI助手',
        'ChatGPT',
        '对话AI',
        '智能问答',
        '语言模型'
    ],
    '内容创作': [
        '写作助手',
        '内容生成',
        '文案创作',
        '博客写作',
        '营销文案'
    ],
    '图像处理': [
        '图像生成',
        '图像编辑',
        'AI绘画',
        '头像生成',
        '背景移除'
    ],
    '视频处理': [
        '视频生成',
        '视频编辑',
        '视频剪辑',
        '短视频制作',
        '视频字幕'
    ],
    '音频处理': [
        '语音合成',
        '语音识别',
        '音乐生成',
        '语音转文字',
        '文字转语音'
    ],
    '代码开发': [
        '代码生成',
        '代码补全',
        '代码审查',
        '开发助手',
        '低代码平台'
    ],
    '数据分析': [
        '数据分析',
        '数据可视化',
        '商业智能',
        '机器学习',
        '深度学习'
    ],
    '办公效率': [
        '办公自动化',
        '文档处理',
        '项目管理',
        '团队协作',
        '笔记工具'
    ],
    '设计工具': [
        'UI设计',
        'Logo设计',
        '网页设计',
        '平面设计',
        '原型设计'
    ],
    '营销工具': [
        'SEO优化',
        '社交媒体营销',
        '邮件营销',
        '内容营销',
        '市场分析'
    ],
    '翻译工具': [
        '机器翻译',
        '实时翻译',
        '文档翻译',
        '语音翻译'
    ]
};
if (typeof globalThis.$RefreshHelpers$ === 'object' && globalThis.$RefreshHelpers !== null) {
    __turbopack_context__.k.registerExports(module, globalThis.$RefreshHelpers$);
}
}}),
"[project]/src/constants/tags-i18n.ts [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname, k: __turbopack_refresh__, m: module } = __turbopack_context__;
{
// 国际化标签配置文件
// 支持多语言的AI工具标签配置
__turbopack_context__.s({
    "getAvailableTagCategoryKeys": (()=>getAvailableTagCategoryKeys),
    "getAvailableTagKeys": (()=>getAvailableTagKeys),
    "getTagCategoryName": (()=>getTagCategoryName),
    "getTagLabel": (()=>getTagLabel),
    "getTagOptions": (()=>getTagOptions),
    "getTagsByCategory": (()=>getTagsByCategory),
    "useTagCategoryName": (()=>useTagCategoryName),
    "useTagLabel": (()=>useTagLabel),
    "useTagOptions": (()=>useTagOptions),
    "useTagsByCategory": (()=>useTagsByCategory)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2d$intl$2f$dist$2f$esm$2f$development$2f$react$2d$client$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$locals$3e$__ = __turbopack_context__.i("[project]/node_modules/next-intl/dist/esm/development/react-client/index.js [app-client] (ecmascript) <locals>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2d$intl$2f$dist$2f$esm$2f$development$2f$server$2f$react$2d$client$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next-intl/dist/esm/development/server/react-client/index.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$constants$2f$tags$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/constants/tags.ts [app-client] (ecmascript)");
var _s = __turbopack_context__.k.signature(), _s1 = __turbopack_context__.k.signature(), _s2 = __turbopack_context__.k.signature(), _s3 = __turbopack_context__.k.signature();
;
;
;
function useTagOptions() {
    _s();
    const t = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2d$intl$2f$dist$2f$esm$2f$development$2f$react$2d$client$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$locals$3e$__["useTranslations"])('tags');
    return __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$constants$2f$tags$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["AVAILABLE_TAG_KEYS"].map((key)=>({
            key,
            label: t(key)
        }));
}
_s(useTagOptions, "h6+q2O3NJKPY5uL0BIJGLIanww8=", false, function() {
    return [
        __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2d$intl$2f$dist$2f$esm$2f$development$2f$react$2d$client$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$locals$3e$__["useTranslations"]
    ];
});
function useTagsByCategory() {
    _s1();
    const t = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2d$intl$2f$dist$2f$esm$2f$development$2f$react$2d$client$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$locals$3e$__["useTranslations"])('tags');
    const categoryT = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2d$intl$2f$dist$2f$esm$2f$development$2f$react$2d$client$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$locals$3e$__["useTranslations"])('tag_categories');
    return Object.entries(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$constants$2f$tags$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["TAG_KEYS_BY_CATEGORY"]).map(([categoryKey, tagKeys])=>({
            key: categoryKey,
            name: categoryT(categoryKey),
            tags: tagKeys.map((tagKey)=>({
                    key: tagKey,
                    label: t(tagKey)
                }))
        }));
}
_s1(useTagsByCategory, "DvaGc65Npsr0P7wxg790xOy56Xw=", false, function() {
    return [
        __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2d$intl$2f$dist$2f$esm$2f$development$2f$react$2d$client$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$locals$3e$__["useTranslations"],
        __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2d$intl$2f$dist$2f$esm$2f$development$2f$react$2d$client$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$locals$3e$__["useTranslations"]
    ];
});
function useTagLabel(tagKey) {
    _s2();
    const t = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2d$intl$2f$dist$2f$esm$2f$development$2f$react$2d$client$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$locals$3e$__["useTranslations"])('tags');
    return t(tagKey) || tagKey;
}
_s2(useTagLabel, "h6+q2O3NJKPY5uL0BIJGLIanww8=", false, function() {
    return [
        __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2d$intl$2f$dist$2f$esm$2f$development$2f$react$2d$client$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$locals$3e$__["useTranslations"]
    ];
});
function useTagCategoryName(categoryKey) {
    _s3();
    const t = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2d$intl$2f$dist$2f$esm$2f$development$2f$react$2d$client$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$locals$3e$__["useTranslations"])('tag_categories');
    return t(categoryKey) || categoryKey;
}
_s3(useTagCategoryName, "h6+q2O3NJKPY5uL0BIJGLIanww8=", false, function() {
    return [
        __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2d$intl$2f$dist$2f$esm$2f$development$2f$react$2d$client$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$locals$3e$__["useTranslations"]
    ];
});
function getAvailableTagKeys() {
    return __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$constants$2f$tags$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["AVAILABLE_TAG_KEYS"];
}
function getAvailableTagCategoryKeys() {
    return Object.keys(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$constants$2f$tags$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["TAG_KEYS_BY_CATEGORY"]);
}
async function getTagOptions(locale) {
    const t = await (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2d$intl$2f$dist$2f$esm$2f$development$2f$server$2f$react$2d$client$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["getTranslations"])({
        locale: locale || 'en',
        namespace: 'tags'
    });
    return __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$constants$2f$tags$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["AVAILABLE_TAG_KEYS"].map((key)=>({
            key,
            label: t(key)
        }));
}
async function getTagsByCategory(locale) {
    const t = await (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2d$intl$2f$dist$2f$esm$2f$development$2f$server$2f$react$2d$client$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["getTranslations"])({
        locale: locale || 'en',
        namespace: 'tags'
    });
    const categoryT = await (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2d$intl$2f$dist$2f$esm$2f$development$2f$server$2f$react$2d$client$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["getTranslations"])({
        locale: locale || 'en',
        namespace: 'tag_categories'
    });
    return Object.entries(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$constants$2f$tags$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["TAG_KEYS_BY_CATEGORY"]).map(([categoryKey, tagKeys])=>({
            key: categoryKey,
            name: categoryT(categoryKey),
            tags: tagKeys.map((tagKey)=>({
                    key: tagKey,
                    label: t(tagKey)
                }))
        }));
}
async function getTagLabel(tagKey, locale) {
    const t = await (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2d$intl$2f$dist$2f$esm$2f$development$2f$server$2f$react$2d$client$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["getTranslations"])({
        locale: locale || 'en',
        namespace: 'tags'
    });
    return t(tagKey) || tagKey;
}
async function getTagCategoryName(categoryKey, locale) {
    const t = await (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2d$intl$2f$dist$2f$esm$2f$development$2f$server$2f$react$2d$client$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["getTranslations"])({
        locale: locale || 'en',
        namespace: 'tag_categories'
    });
    return t(categoryKey) || categoryKey;
}
if (typeof globalThis.$RefreshHelpers$ === 'object' && globalThis.$RefreshHelpers !== null) {
    __turbopack_context__.k.registerExports(module, globalThis.$RefreshHelpers$);
}
}}),
"[project]/src/components/TagSelector.tsx [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname, k: __turbopack_refresh__, m: module } = __turbopack_context__;
{
__turbopack_context__.s({
    "default": (()=>TagSelector)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/compiled/react/jsx-dev-runtime.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/compiled/react/index.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$i18n$2f$routing$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/i18n/routing.ts [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2d$intl$2f$dist$2f$esm$2f$development$2f$react$2d$client$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$locals$3e$__ = __turbopack_context__.i("[project]/node_modules/next-intl/dist/esm/development/react-client/index.js [app-client] (ecmascript) <locals>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$tag$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__Tag$3e$__ = __turbopack_context__.i("[project]/node_modules/lucide-react/dist/esm/icons/tag.js [app-client] (ecmascript) <export default as Tag>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$search$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__Search$3e$__ = __turbopack_context__.i("[project]/node_modules/lucide-react/dist/esm/icons/search.js [app-client] (ecmascript) <export default as Search>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$x$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__X$3e$__ = __turbopack_context__.i("[project]/node_modules/lucide-react/dist/esm/icons/x.js [app-client] (ecmascript) <export default as X>");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$constants$2f$tags$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/constants/tags.ts [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$constants$2f$tags$2d$i18n$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/constants/tags-i18n.ts [app-client] (ecmascript)");
;
var _s = __turbopack_context__.k.signature();
'use client';
;
;
;
;
;
;
function TagSelector({ selectedTags, onTagsChange, maxTags = __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$constants$2f$tags$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["MAX_TAGS_COUNT"], placeholder }) {
    _s();
    const [searchTerm, setSearchTerm] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useState"])('');
    const [isOpen, setIsOpen] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useState"])(false);
    const pathname = (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$i18n$2f$routing$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["usePathname"])();
    const t = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2d$intl$2f$dist$2f$esm$2f$development$2f$react$2d$client$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$locals$3e$__["useTranslations"])('common');
    const tagOptions = (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$constants$2f$tags$2d$i18n$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useTagOptions"])();
    // Extract current locale from pathname
    const currentLocale = pathname?.startsWith('/en') ? 'en' : 'zh';
    const toggleTag = (tagKey)=>{
        if (selectedTags.includes(tagKey)) {
            onTagsChange(selectedTags.filter((t)=>t !== tagKey));
        } else if (selectedTags.length < maxTags) {
            onTagsChange([
                ...selectedTags,
                tagKey
            ]);
        }
    };
    const removeTag = (tagKey)=>{
        onTagsChange(selectedTags.filter((t)=>t !== tagKey));
    };
    // 过滤标签：根据搜索词过滤，并排除已选择的标签
    const filteredTags = tagOptions.filter((tag)=>tag.label.toLowerCase().includes(searchTerm.toLowerCase()) && !selectedTags.includes(tag.key));
    // 获取已选择标签的显示文本
    const getSelectedTagLabel = (tagKey)=>{
        const tagOption = tagOptions.find((tag)=>tag.key === tagKey);
        return tagOption ? tagOption.label : tagKey;
    };
    return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
        className: "space-y-4",
        children: [
            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                className: "flex items-center justify-between",
                children: [
                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("h3", {
                        className: "text-lg font-medium text-gray-900",
                        children: t('select_tags')
                    }, void 0, false, {
                        fileName: "[project]/src/components/TagSelector.tsx",
                        lineNumber: 62,
                        columnNumber: 9
                    }, this),
                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("span", {
                        className: "text-sm text-gray-500",
                        children: t('selected_count', {
                            count: selectedTags.length,
                            max: maxTags
                        })
                    }, void 0, false, {
                        fileName: "[project]/src/components/TagSelector.tsx",
                        lineNumber: 63,
                        columnNumber: 9
                    }, this)
                ]
            }, void 0, true, {
                fileName: "[project]/src/components/TagSelector.tsx",
                lineNumber: 61,
                columnNumber: 7
            }, this),
            selectedTags.length > 0 && /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                className: "space-y-2",
                children: [
                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("h4", {
                        className: "text-sm font-medium text-gray-700",
                        children: t('selected_tags')
                    }, void 0, false, {
                        fileName: "[project]/src/components/TagSelector.tsx",
                        lineNumber: 71,
                        columnNumber: 11
                    }, this),
                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                        className: "flex flex-wrap gap-2",
                        children: selectedTags.map((tagKey)=>/*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("span", {
                                className: "inline-flex items-center px-3 py-1 rounded-md text-sm font-medium bg-blue-100 text-blue-800",
                                children: [
                                    getSelectedTagLabel(tagKey),
                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("button", {
                                        type: "button",
                                        onClick: ()=>removeTag(tagKey),
                                        className: "ml-2 text-blue-600 hover:text-blue-800",
                                        children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$x$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__X$3e$__["X"], {
                                            className: "h-3 w-3"
                                        }, void 0, false, {
                                            fileName: "[project]/src/components/TagSelector.tsx",
                                            lineNumber: 84,
                                            columnNumber: 19
                                        }, this)
                                    }, void 0, false, {
                                        fileName: "[project]/src/components/TagSelector.tsx",
                                        lineNumber: 79,
                                        columnNumber: 17
                                    }, this)
                                ]
                            }, tagKey, true, {
                                fileName: "[project]/src/components/TagSelector.tsx",
                                lineNumber: 74,
                                columnNumber: 15
                            }, this))
                    }, void 0, false, {
                        fileName: "[project]/src/components/TagSelector.tsx",
                        lineNumber: 72,
                        columnNumber: 11
                    }, this)
                ]
            }, void 0, true, {
                fileName: "[project]/src/components/TagSelector.tsx",
                lineNumber: 70,
                columnNumber: 9
            }, this),
            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                className: "space-y-3",
                children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                    className: "relative",
                    children: [
                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("label", {
                            className: "block text-sm font-medium text-gray-700 mb-2",
                            children: t('select_tags_max', {
                                max: maxTags
                            })
                        }, void 0, false, {
                            fileName: "[project]/src/components/TagSelector.tsx",
                            lineNumber: 95,
                            columnNumber: 11
                        }, this),
                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                            className: "relative mb-3",
                            children: [
                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("input", {
                                    type: "text",
                                    placeholder: placeholder || t('search_tags'),
                                    value: searchTerm,
                                    onChange: (e)=>setSearchTerm(e.target.value),
                                    onFocus: ()=>setIsOpen(true),
                                    className: "w-full pl-10 pr-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                                }, void 0, false, {
                                    fileName: "[project]/src/components/TagSelector.tsx",
                                    lineNumber: 101,
                                    columnNumber: 13
                                }, this),
                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$search$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__Search$3e$__["Search"], {
                                    className: "absolute left-3 top-2.5 h-4 w-4 text-gray-400"
                                }, void 0, false, {
                                    fileName: "[project]/src/components/TagSelector.tsx",
                                    lineNumber: 109,
                                    columnNumber: 13
                                }, this)
                            ]
                        }, void 0, true, {
                            fileName: "[project]/src/components/TagSelector.tsx",
                            lineNumber: 100,
                            columnNumber: 11
                        }, this),
                        (isOpen || searchTerm) && /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                            className: "relative",
                            children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                className: "absolute z-10 w-full bg-white border border-gray-300 rounded-lg shadow-lg max-h-60 overflow-y-auto",
                                children: filteredTags.length > 0 ? /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                    className: "p-2",
                                    children: [
                                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                            className: "grid grid-cols-1 gap-1",
                                            children: filteredTags.map((tag)=>{
                                                const isDisabled = selectedTags.length >= maxTags;
                                                return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("button", {
                                                    type: "button",
                                                    onClick: ()=>{
                                                        toggleTag(tag.key);
                                                        setSearchTerm('');
                                                        setIsOpen(false);
                                                    },
                                                    disabled: isDisabled,
                                                    className: `
                              w-full px-3 py-2 text-sm text-left rounded-md transition-colors
                              ${isDisabled ? 'bg-gray-100 text-gray-400 cursor-not-allowed' : 'hover:bg-blue-50 text-gray-700'}
                            `,
                                                    children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                                        className: "flex items-center",
                                                        children: [
                                                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$tag$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__Tag$3e$__["Tag"], {
                                                                className: "h-3 w-3 mr-2 text-gray-400"
                                                            }, void 0, false, {
                                                                fileName: "[project]/src/components/TagSelector.tsx",
                                                                lineNumber: 141,
                                                                columnNumber: 31
                                                            }, this),
                                                            tag.label
                                                        ]
                                                    }, void 0, true, {
                                                        fileName: "[project]/src/components/TagSelector.tsx",
                                                        lineNumber: 140,
                                                        columnNumber: 29
                                                    }, this)
                                                }, tag.key, false, {
                                                    fileName: "[project]/src/components/TagSelector.tsx",
                                                    lineNumber: 123,
                                                    columnNumber: 27
                                                }, this);
                                            })
                                        }, void 0, false, {
                                            fileName: "[project]/src/components/TagSelector.tsx",
                                            lineNumber: 118,
                                            columnNumber: 21
                                        }, this),
                                        filteredTags.length > 50 && /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("p", {
                                            className: "text-xs text-gray-500 mt-2 px-3",
                                            children: t('found_tags', {
                                                count: filteredTags.length
                                            })
                                        }, void 0, false, {
                                            fileName: "[project]/src/components/TagSelector.tsx",
                                            lineNumber: 149,
                                            columnNumber: 23
                                        }, this)
                                    ]
                                }, void 0, true, {
                                    fileName: "[project]/src/components/TagSelector.tsx",
                                    lineNumber: 117,
                                    columnNumber: 19
                                }, this) : /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                    className: "p-4 text-center text-gray-500 text-sm",
                                    children: searchTerm ? t('no_tags_found') : t('start_typing')
                                }, void 0, false, {
                                    fileName: "[project]/src/components/TagSelector.tsx",
                                    lineNumber: 155,
                                    columnNumber: 19
                                }, this)
                            }, void 0, false, {
                                fileName: "[project]/src/components/TagSelector.tsx",
                                lineNumber: 115,
                                columnNumber: 15
                            }, this)
                        }, void 0, false, {
                            fileName: "[project]/src/components/TagSelector.tsx",
                            lineNumber: 114,
                            columnNumber: 13
                        }, this)
                    ]
                }, void 0, true, {
                    fileName: "[project]/src/components/TagSelector.tsx",
                    lineNumber: 94,
                    columnNumber: 9
                }, this)
            }, void 0, false, {
                fileName: "[project]/src/components/TagSelector.tsx",
                lineNumber: 93,
                columnNumber: 7
            }, this),
            (isOpen || searchTerm) && /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                className: "fixed inset-0 z-0",
                onClick: ()=>{
                    setIsOpen(false);
                    setSearchTerm('');
                }
            }, void 0, false, {
                fileName: "[project]/src/components/TagSelector.tsx",
                lineNumber: 167,
                columnNumber: 9
            }, this),
            selectedTags.length >= maxTags && /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("p", {
                className: "text-sm text-amber-600",
                children: t('max_tags_limit', {
                    max: maxTags
                })
            }, void 0, false, {
                fileName: "[project]/src/components/TagSelector.tsx",
                lineNumber: 178,
                columnNumber: 9
            }, this)
        ]
    }, void 0, true, {
        fileName: "[project]/src/components/TagSelector.tsx",
        lineNumber: 59,
        columnNumber: 5
    }, this);
}
_s(TagSelector, "EwpCqBjJ47l1kWeMfNAiwRaM7l0=", false, function() {
    return [
        __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$i18n$2f$routing$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["usePathname"],
        __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2d$intl$2f$dist$2f$esm$2f$development$2f$react$2d$client$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$locals$3e$__["useTranslations"],
        __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$constants$2f$tags$2d$i18n$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useTagOptions"]
    ];
});
_c = TagSelector;
var _c;
__turbopack_context__.k.register(_c, "TagSelector");
if (typeof globalThis.$RefreshHelpers$ === 'object' && globalThis.$RefreshHelpers !== null) {
    __turbopack_context__.k.registerExports(module, globalThis.$RefreshHelpers$);
}
}}),
}]);

//# sourceMappingURL=src_511fcdaa._.js.map