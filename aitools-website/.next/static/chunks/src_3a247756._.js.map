{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/workspace/aitools/aitools-website/src/components/providers/SessionProvider.tsx"], "sourcesContent": ["'use client';\n\nimport { SessionProvider as NextAuthSessionProvider } from 'next-auth/react';\nimport { ReactNode } from 'react';\n\ninterface SessionProviderProps {\n  children: ReactNode;\n}\n\nexport default function SessionProvider({ children }: SessionProviderProps) {\n  return (\n    <NextAuthSessionProvider>\n      {children}\n    </NextAuthSessionProvider>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AAFA;;;AASe,SAAS,gBAAgB,EAAE,QAAQ,EAAwB;IACxE,qBACE,6LAAC,iJAAA,CAAA,kBAAuB;kBACrB;;;;;;AAGP;KANwB", "debugId": null}}, {"offset": {"line": 36, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/workspace/aitools/aitools-website/src/i18n/routing.ts"], "sourcesContent": ["import { defineRouting } from 'next-intl/routing';\nimport { createNavigation } from 'next-intl/navigation';\n\nexport const routing = defineRouting({\n  // 支持的语言列表\n  locales: ['en', 'zh'],\n  \n  // 默认语言\n  defaultLocale: 'en',\n  \n  // 语言前缀配置 - 始终显示语言前缀\n  localePrefix: 'always',\n  \n  // 启用语言检测\n  localeDetection: true,\n  \n  // 启用备用链接\n  alternateLinks: true,\n  \n  // 语言 cookie 配置\n  localeCookie: {\n    name: 'NEXT_LOCALE',\n    sameSite: 'lax',\n    maxAge: 60 * 60 * 24 * 365 // 1 year\n  }\n});\n\n// 轻量级的包装器，围绕 Next.js 的导航 API\n// 它们将自动处理用户的语言环境\nexport const { Link, redirect, usePathname, useRouter } = createNavigation(routing);\n"], "names": [], "mappings": ";;;;;;;AAAA;AACA;;;AAEO,MAAM,UAAU,CAAA,GAAA,qOAAA,CAAA,gBAAa,AAAD,EAAE;IACnC,UAAU;IACV,SAAS;QAAC;QAAM;KAAK;IAErB,OAAO;IACP,eAAe;IAEf,oBAAoB;IACpB,cAAc;IAEd,SAAS;IACT,iBAAiB;IAEjB,SAAS;IACT,gBAAgB;IAEhB,eAAe;IACf,cAAc;QACZ,MAAM;QACN,UAAU;QACV,QAAQ,KAAK,KAAK,KAAK,IAAI,SAAS;IACtC;AACF;AAIO,MAAM,EAAE,IAAI,EAAE,QAAQ,EAAE,WAAW,EAAE,SAAS,EAAE,GAAG,CAAA,GAAA,iQAAA,CAAA,mBAAgB,AAAD,EAAE", "debugId": null}}, {"offset": {"line": 78, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/workspace/aitools/aitools-website/src/components/auth/LoginModal.tsx"], "sourcesContent": ["'use client';\n\nimport { useState } from 'react';\nimport { signIn } from 'next-auth/react';\nimport { usePathname } from '@/i18n/routing';\nimport { useTranslations, useLocale } from 'next-intl';\nimport { FaGoogle, FaGithub, FaEnvelope, FaTimes } from 'react-icons/fa';\nimport { Locale } from '@/i18n/config';\n\ninterface LoginModalProps {\n  isOpen: boolean;\n  onClose: () => void;\n}\n\ntype LoginStep = 'method' | 'email' | 'code';\n\nexport default function LoginModal({ isOpen, onClose }: LoginModalProps) {\n  const [step, setStep] = useState<LoginStep>('method');\n  const [email, setEmail] = useState('');\n  const [verificationToken, setVerificationToken] = useState('');\n  const [isLoading, setIsLoading] = useState(false);\n  const [emailError, setEmailError] = useState('');\n\n  const pathname = usePathname();\n  const t = useTranslations('auth');\n  const locale = useLocale() as Locale;\n\n  const showToast = (message: string, type: 'success' | 'error' = 'success') => {\n    // Simple toast implementation\n    const toast = document.createElement('div');\n    toast.className = `fixed top-4 right-4 p-4 rounded-lg text-white z-50 ${\n      type === 'success' ? 'bg-green-500' : 'bg-red-500'\n    }`;\n    toast.textContent = message;\n    document.body.appendChild(toast);\n    setTimeout(() => document.body.removeChild(toast), 3000);\n  };\n\n  const handleClose = () => {\n    setStep('method');\n    setEmail('');\n    setVerificationToken('');\n    setEmailError('');\n    onClose();\n  };\n\n  const handleOAuthLogin = async (provider: 'google' | 'github') => {\n    try {\n      setIsLoading(true);\n      await signIn(provider, { callbackUrl: '/' });\n    } catch (error) {\n      showToast(t('login_failed'), 'error');\n    } finally {\n      setIsLoading(false);\n    }\n  };\n\n  const handleEmailSubmit = async () => {\n    if (!email) {\n      setEmailError(t('email_required'));\n      return;\n    }\n\n    const emailRegex = /^[^\\s@]+@[^\\s@]+\\.[^\\s@]+$/;\n    if (!emailRegex.test(email)) {\n      setEmailError(t('email_invalid'));\n      return;\n    }\n\n    setEmailError('');\n    setIsLoading(true);\n\n    try {\n      const response = await fetch('/api/auth/send-code', {\n        method: 'POST',\n        headers: {\n          'Content-Type': 'application/json',\n        },\n        body: JSON.stringify({ email }),\n      });\n\n      const data = await response.json();\n\n      if (data.success) {\n        setVerificationToken(data.token);\n        setStep('code');\n        showToast(t('verification_sent'));\n      } else {\n        showToast(data.error || t('send_failed'), 'error');\n      }\n    } catch (error) {\n      showToast(t('network_error'), 'error');\n    } finally {\n      setIsLoading(false);\n    }\n  };\n\n  const handleCodeVerify = async (code: string) => {\n    if (code.length !== 6) return;\n\n    setIsLoading(true);\n\n    try {\n      const result = await signIn('email-code', {\n        email,\n        code,\n        token: verificationToken,\n        redirect: false,\n      });\n\n      if (result?.ok) {\n        showToast(t('login_success'));\n        handleClose();\n        // NextAuth会自动更新session，不需要手动刷新页面\n      } else {\n        showToast(result?.error || t('verification_error'), 'error');\n      }\n    } catch (error) {\n      showToast(t('network_error'), 'error');\n    } finally {\n      setIsLoading(false);\n    }\n  };\n\n  const renderMethodStep = () => (\n    <div className=\"space-y-4\">\n      <p className=\"text-gray-600 text-center\">\n        {t('choose_method')}\n      </p>\n\n      <div className=\"space-y-3\">\n        <button\n          className=\"w-full flex items-center justify-center gap-3 px-4 py-3 border border-red-300 rounded-lg text-red-700 hover:bg-red-50 transition-colors disabled:opacity-50\"\n          onClick={() => handleOAuthLogin('google')}\n          disabled={isLoading}\n        >\n          <FaGoogle />\n          {t('google_login')}\n        </button>\n\n        <button\n          className=\"w-full flex items-center justify-center gap-3 px-4 py-3 border border-gray-300 rounded-lg text-gray-700 hover:bg-gray-50 transition-colors disabled:opacity-50\"\n          onClick={() => handleOAuthLogin('github')}\n          disabled={isLoading}\n        >\n          <FaGithub />\n          {t('github_login')}\n        </button>\n      </div>\n\n      <div className=\"relative\">\n        <div className=\"absolute inset-0 flex items-center\">\n          <div className=\"w-full border-t border-gray-300\" />\n        </div>\n        <div className=\"relative flex justify-center text-sm\">\n          <span className=\"px-2 bg-white text-gray-500\">{t('or')}</span>\n        </div>\n      </div>\n\n      <button\n        className=\"w-full flex items-center justify-center gap-3 px-4 py-3 border border-blue-300 rounded-lg text-blue-700 hover:bg-blue-50 transition-colors\"\n        onClick={() => setStep('email')}\n      >\n        <FaEnvelope />\n        {t('email_login')}\n      </button>\n    </div>\n  );\n\n  const renderEmailStep = () => (\n    <div className=\"space-y-4\">\n      <p className=\"text-gray-600 text-center\">\n        {t('email_instruction')}\n      </p>\n\n      <div>\n        <label className=\"block text-sm font-medium text-gray-700 mb-2\">\n          {t('email_address')}\n        </label>\n        <input\n          type=\"email\"\n          value={email}\n          onChange={(e) => setEmail(e.target.value)}\n          placeholder={t('email_placeholder')}\n          onKeyPress={(e) => e.key === 'Enter' && handleEmailSubmit()}\n          className=\"w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500\"\n        />\n        {emailError && (\n          <p className=\"mt-1 text-sm text-red-600\">{emailError}</p>\n        )}\n      </div>\n\n      <div className=\"space-y-3\">\n        <button\n          className=\"w-full px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors disabled:opacity-50\"\n          onClick={handleEmailSubmit}\n          disabled={isLoading}\n        >\n          {isLoading ? t('sending') : t('send_code')}\n        </button>\n\n        <button\n          className=\"w-full px-4 py-2 text-gray-600 hover:text-gray-800 transition-colors\"\n          onClick={() => setStep('method')}\n        >\n          {t('back')}\n        </button>\n      </div>\n    </div>\n  );\n\n  const handleCodeInputChange = (index: number, value: string) => {\n    if (value.length > 1) return;\n\n    const inputs = document.querySelectorAll('.code-input') as NodeListOf<HTMLInputElement>;\n    inputs[index].value = value;\n\n    // Auto-focus next input\n    if (value && index < 5) {\n      inputs[index + 1]?.focus();\n    }\n\n    // Check if all inputs are filled\n    const code = Array.from(inputs).map(input => input.value).join('');\n    if (code.length === 6) {\n      handleCodeVerify(code);\n    }\n  };\n\n  const renderCodeStep = () => (\n    <div className=\"space-y-4\">\n      <p className=\"text-gray-600 text-center\">\n        {t('verification_instruction', { email })}\n      </p>\n\n      <div>\n        <label className=\"block text-sm font-medium text-gray-700 mb-2\">\n          {t('verification_code')}\n        </label>\n        <div className=\"flex justify-center gap-2\">\n          {[0, 1, 2, 3, 4, 5].map((index) => (\n            <input\n              key={index}\n              type=\"text\"\n              maxLength={1}\n              onChange={(e) => handleCodeInputChange(index, e.target.value)}\n              disabled={isLoading}\n              className=\"code-input w-12 h-12 text-center text-lg border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 disabled:opacity-50\"\n            />\n          ))}\n        </div>\n      </div>\n\n      <div className=\"space-y-3\">\n        <button\n          className=\"w-full px-4 py-2 text-sm text-gray-600 hover:text-gray-800 transition-colors\"\n          onClick={() => setStep('email')}\n        >\n          {t('resend_code')}\n        </button>\n\n        <button\n          className=\"w-full px-4 py-2 text-gray-600 hover:text-gray-800 transition-colors\"\n          onClick={() => setStep('method')}\n        >\n          {t('back')}\n        </button>\n      </div>\n    </div>\n  );\n\n  if (!isOpen) return null;\n\n  return (\n    <div className=\"fixed inset-0 z-50 flex items-center justify-center\">\n      {/* Overlay */}\n      <div\n        className=\"absolute inset-0 bg-black bg-opacity-50\"\n        onClick={handleClose}\n      />\n\n      {/* Modal */}\n      <div className=\"relative bg-white rounded-lg shadow-xl max-w-md w-full mx-4\">\n        {/* Header */}\n        <div className=\"flex items-center justify-between p-6 border-b\">\n          <h2 className=\"text-xl font-semibold text-gray-900 text-center flex-1\">\n            {step === 'method' && t('login_title')}\n            {step === 'email' && t('email_login_title')}\n            {step === 'code' && t('verification_title')}\n          </h2>\n          <button\n            onClick={handleClose}\n            className=\"text-gray-400 hover:text-gray-600 transition-colors\"\n          >\n            <FaTimes />\n          </button>\n        </div>\n\n        {/* Body */}\n        <div className=\"p-6\">\n          {step === 'method' && renderMethodStep()}\n          {step === 'email' && renderEmailStep()}\n          {step === 'code' && renderCodeStep()}\n        </div>\n      </div>\n    </div>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AAAA;AACA;;;AANA;;;;;;AAgBe,SAAS,WAAW,EAAE,MAAM,EAAE,OAAO,EAAmB;;IACrE,MAAM,CAAC,MAAM,QAAQ,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAa;IAC5C,MAAM,CAAC,OAAO,SAAS,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACnC,MAAM,CAAC,mBAAmB,qBAAqB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAC3D,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAC3C,MAAM,CAAC,YAAY,cAAc,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAE7C,MAAM,WAAW,CAAA,GAAA,yHAAA,CAAA,cAAW,AAAD;IAC3B,MAAM,IAAI,CAAA,GAAA,yMAAA,CAAA,kBAAe,AAAD,EAAE;IAC1B,MAAM,SAAS,CAAA,GAAA,qKAAA,CAAA,YAAS,AAAD;IAEvB,MAAM,YAAY,CAAC,SAAiB,OAA4B,SAAS;QACvE,8BAA8B;QAC9B,MAAM,QAAQ,SAAS,aAAa,CAAC;QACrC,MAAM,SAAS,GAAG,CAAC,mDAAmD,EACpE,SAAS,YAAY,iBAAiB,cACtC;QACF,MAAM,WAAW,GAAG;QACpB,SAAS,IAAI,CAAC,WAAW,CAAC;QAC1B,WAAW,IAAM,SAAS,IAAI,CAAC,WAAW,CAAC,QAAQ;IACrD;IAEA,MAAM,cAAc;QAClB,QAAQ;QACR,SAAS;QACT,qBAAqB;QACrB,cAAc;QACd;IACF;IAEA,MAAM,mBAAmB,OAAO;QAC9B,IAAI;YACF,aAAa;YACb,MAAM,CAAA,GAAA,iJAAA,CAAA,SAAM,AAAD,EAAE,UAAU;gBAAE,aAAa;YAAI;QAC5C,EAAE,OAAO,OAAO;YACd,UAAU,EAAE,iBAAiB;QAC/B,SAAU;YACR,aAAa;QACf;IACF;IAEA,MAAM,oBAAoB;QACxB,IAAI,CAAC,OAAO;YACV,cAAc,EAAE;YAChB;QACF;QAEA,MAAM,aAAa;QACnB,IAAI,CAAC,WAAW,IAAI,CAAC,QAAQ;YAC3B,cAAc,EAAE;YAChB;QACF;QAEA,cAAc;QACd,aAAa;QAEb,IAAI;YACF,MAAM,WAAW,MAAM,MAAM,uBAAuB;gBAClD,QAAQ;gBACR,SAAS;oBACP,gBAAgB;gBAClB;gBACA,MAAM,KAAK,SAAS,CAAC;oBAAE;gBAAM;YAC/B;YAEA,MAAM,OAAO,MAAM,SAAS,IAAI;YAEhC,IAAI,KAAK,OAAO,EAAE;gBAChB,qBAAqB,KAAK,KAAK;gBAC/B,QAAQ;gBACR,UAAU,EAAE;YACd,OAAO;gBACL,UAAU,KAAK,KAAK,IAAI,EAAE,gBAAgB;YAC5C;QACF,EAAE,OAAO,OAAO;YACd,UAAU,EAAE,kBAAkB;QAChC,SAAU;YACR,aAAa;QACf;IACF;IAEA,MAAM,mBAAmB,OAAO;QAC9B,IAAI,KAAK,MAAM,KAAK,GAAG;QAEvB,aAAa;QAEb,IAAI;YACF,MAAM,SAAS,MAAM,CAAA,GAAA,iJAAA,CAAA,SAAM,AAAD,EAAE,cAAc;gBACxC;gBACA;gBACA,OAAO;gBACP,UAAU;YACZ;YAEA,IAAI,QAAQ,IAAI;gBACd,UAAU,EAAE;gBACZ;YACA,iCAAiC;YACnC,OAAO;gBACL,UAAU,QAAQ,SAAS,EAAE,uBAAuB;YACtD;QACF,EAAE,OAAO,OAAO;YACd,UAAU,EAAE,kBAAkB;QAChC,SAAU;YACR,aAAa;QACf;IACF;IAEA,MAAM,mBAAmB,kBACvB,6LAAC;YAAI,WAAU;;8BACb,6LAAC;oBAAE,WAAU;8BACV,EAAE;;;;;;8BAGL,6LAAC;oBAAI,WAAU;;sCACb,6LAAC;4BACC,WAAU;4BACV,SAAS,IAAM,iBAAiB;4BAChC,UAAU;;8CAEV,6LAAC,iJAAA,CAAA,WAAQ;;;;;gCACR,EAAE;;;;;;;sCAGL,6LAAC;4BACC,WAAU;4BACV,SAAS,IAAM,iBAAiB;4BAChC,UAAU;;8CAEV,6LAAC,iJAAA,CAAA,WAAQ;;;;;gCACR,EAAE;;;;;;;;;;;;;8BAIP,6LAAC;oBAAI,WAAU;;sCACb,6LAAC;4BAAI,WAAU;sCACb,cAAA,6LAAC;gCAAI,WAAU;;;;;;;;;;;sCAEjB,6LAAC;4BAAI,WAAU;sCACb,cAAA,6LAAC;gCAAK,WAAU;0CAA+B,EAAE;;;;;;;;;;;;;;;;;8BAIrD,6LAAC;oBACC,WAAU;oBACV,SAAS,IAAM,QAAQ;;sCAEvB,6LAAC,iJAAA,CAAA,aAAU;;;;;wBACV,EAAE;;;;;;;;;;;;;IAKT,MAAM,kBAAkB,kBACtB,6LAAC;YAAI,WAAU;;8BACb,6LAAC;oBAAE,WAAU;8BACV,EAAE;;;;;;8BAGL,6LAAC;;sCACC,6LAAC;4BAAM,WAAU;sCACd,EAAE;;;;;;sCAEL,6LAAC;4BACC,MAAK;4BACL,OAAO;4BACP,UAAU,CAAC,IAAM,SAAS,EAAE,MAAM,CAAC,KAAK;4BACxC,aAAa,EAAE;4BACf,YAAY,CAAC,IAAM,EAAE,GAAG,KAAK,WAAW;4BACxC,WAAU;;;;;;wBAEX,4BACC,6LAAC;4BAAE,WAAU;sCAA6B;;;;;;;;;;;;8BAI9C,6LAAC;oBAAI,WAAU;;sCACb,6LAAC;4BACC,WAAU;4BACV,SAAS;4BACT,UAAU;sCAET,YAAY,EAAE,aAAa,EAAE;;;;;;sCAGhC,6LAAC;4BACC,WAAU;4BACV,SAAS,IAAM,QAAQ;sCAEtB,EAAE;;;;;;;;;;;;;;;;;;IAMX,MAAM,wBAAwB,CAAC,OAAe;QAC5C,IAAI,MAAM,MAAM,GAAG,GAAG;QAEtB,MAAM,SAAS,SAAS,gBAAgB,CAAC;QACzC,MAAM,CAAC,MAAM,CAAC,KAAK,GAAG;QAEtB,wBAAwB;QACxB,IAAI,SAAS,QAAQ,GAAG;YACtB,MAAM,CAAC,QAAQ,EAAE,EAAE;QACrB;QAEA,iCAAiC;QACjC,MAAM,OAAO,MAAM,IAAI,CAAC,QAAQ,GAAG,CAAC,CAAA,QAAS,MAAM,KAAK,EAAE,IAAI,CAAC;QAC/D,IAAI,KAAK,MAAM,KAAK,GAAG;YACrB,iBAAiB;QACnB;IACF;IAEA,MAAM,iBAAiB,kBACrB,6LAAC;YAAI,WAAU;;8BACb,6LAAC;oBAAE,WAAU;8BACV,EAAE,4BAA4B;wBAAE;oBAAM;;;;;;8BAGzC,6LAAC;;sCACC,6LAAC;4BAAM,WAAU;sCACd,EAAE;;;;;;sCAEL,6LAAC;4BAAI,WAAU;sCACZ;gCAAC;gCAAG;gCAAG;gCAAG;gCAAG;gCAAG;6BAAE,CAAC,GAAG,CAAC,CAAC,sBACvB,6LAAC;oCAEC,MAAK;oCACL,WAAW;oCACX,UAAU,CAAC,IAAM,sBAAsB,OAAO,EAAE,MAAM,CAAC,KAAK;oCAC5D,UAAU;oCACV,WAAU;mCALL;;;;;;;;;;;;;;;;8BAWb,6LAAC;oBAAI,WAAU;;sCACb,6LAAC;4BACC,WAAU;4BACV,SAAS,IAAM,QAAQ;sCAEtB,EAAE;;;;;;sCAGL,6LAAC;4BACC,WAAU;4BACV,SAAS,IAAM,QAAQ;sCAEtB,EAAE;;;;;;;;;;;;;;;;;;IAMX,IAAI,CAAC,QAAQ,OAAO;IAEpB,qBACE,6LAAC;QAAI,WAAU;;0BAEb,6LAAC;gBACC,WAAU;gBACV,SAAS;;;;;;0BAIX,6LAAC;gBAAI,WAAU;;kCAEb,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;gCAAG,WAAU;;oCACX,SAAS,YAAY,EAAE;oCACvB,SAAS,WAAW,EAAE;oCACtB,SAAS,UAAU,EAAE;;;;;;;0CAExB,6LAAC;gCACC,SAAS;gCACT,WAAU;0CAEV,cAAA,6LAAC,iJAAA,CAAA,UAAO;;;;;;;;;;;;;;;;kCAKZ,6LAAC;wBAAI,WAAU;;4BACZ,SAAS,YAAY;4BACrB,SAAS,WAAW;4BACpB,SAAS,UAAU;;;;;;;;;;;;;;;;;;;AAK9B;GAnSwB;;QAOL,yHAAA,CAAA,cAAW;QAClB,yMAAA,CAAA,kBAAe;QACV,qKAAA,CAAA,YAAS;;;KATF", "debugId": null}}, {"offset": {"line": 581, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/workspace/aitools/aitools-website/src/components/auth/UserMenuClient.tsx"], "sourcesContent": ["'use client';\n\nimport { useState } from 'react';\nimport { useSession, signOut } from 'next-auth/react';\nimport { Fa<PERSON>ser, FaHeart, FaPlus, FaCog, FaSignOutAlt, FaSignInAlt, FaChevronDown, FaList } from 'react-icons/fa';\nimport { useRouter, Link as NextLink } from '@/i18n/routing';\nimport { useTranslations, useLocale } from 'next-intl';\nimport LoginModal from './LoginModal';\nimport { Locale } from '@/i18n/config';\n\n\nconst NavLink = ({ children, href, locale }: { children: React.ReactNode; href: string; locale: string }) => (\n  <NextLink\n    href={href}\n    locale={locale}\n    className=\"w-full flex items-center gap-3 px-4 py-2 text-sm text-gray-700 hover:bg-gray-100 transition-colors\"\n  >\n    {children}\n  </NextLink>\n);\n\nexport default function UserMenuClient({ locale }: { locale: Locale }) {\n  const { data: session, status } = useSession();\n  const router = useRouter();\n  const [isLoginModalOpen, setIsLoginModalOpen] = useState(false);\n  const [isMenuOpen, setIsMenuOpen] = useState(false);\n\n  const t = useTranslations('common');\n\n  const handleSignOut = async () => {\n    await signOut({ callbackUrl: '/' });\n  };\n\n  const handleNavigation = (path: string) => {\n    setIsMenuOpen(false);\n    router.push(path);\n  };\n\n  // 如果正在加载，显示加载状态\n  if (status === 'loading') {\n    return (\n      <button\n        className=\"px-3 py-2 text-sm text-gray-600 bg-gray-100 rounded-lg animate-pulse\"\n        disabled\n      >\n        {t('loading')}\n      </button>\n    );\n  }\n\n  // 如果未登录，显示登录按钮\n  if (!session) {\n    return (\n      <>\n        <button\n          className=\"flex items-center gap-2 px-3 py-2 text-sm border border-blue-300 text-blue-700 rounded-lg hover:bg-blue-50 transition-colors\"\n          onClick={() => setIsLoginModalOpen(true)}\n        >\n          <FaSignInAlt />\n          {t('login')}\n        </button>\n        <LoginModal isOpen={isLoginModalOpen} onClose={() => setIsLoginModalOpen(false)} />\n      </>\n    );\n  }\n\n  // 如果已登录，显示用户菜单\n  return (\n    <div className=\"relative\">\n      <button\n        className=\"flex items-center gap-2 p-1 rounded-lg hover:bg-gray-100 transition-colors\"\n        onClick={() => setIsMenuOpen(!isMenuOpen)}\n      >\n        <div className=\"w-8 h-8 rounded-full bg-gray-300 flex items-center justify-center overflow-hidden\">\n          {session.user?.image ? (\n            <img\n              src={session.user.image}\n              alt={session.user.name || ''}\n              className=\"w-full h-full object-cover\"\n            />\n          ) : (\n            <span className=\"text-sm font-medium text-gray-600\">\n              {session.user?.name?.charAt(0) || 'U'}\n            </span>\n          )}\n        </div>\n        <span className=\"text-sm hidden md:block\">\n          {session.user?.name}\n        </span>\n        <FaChevronDown className={`text-xs transition-transform ${isMenuOpen ? 'rotate-180' : ''}`} />\n      </button>\n\n      {isMenuOpen && (\n        <>\n          {/* Backdrop */}\n          <div\n            className=\"fixed inset-0 z-10\"\n            onClick={() => setIsMenuOpen(false)}\n          />\n\n          {/* Menu */}\n          <div \n            className=\"absolute right-0 mt-2 w-64 bg-white rounded-lg shadow-lg border z-20\"\n            onClick={() => setIsMenuOpen(!isMenuOpen)}\n          >\n            {/* 用户信息 */}\n            <div className=\"p-4 border-b\">\n              <div className=\"flex items-center gap-3\">\n                <div className=\"w-10 h-10 rounded-full bg-gray-300 flex items-center justify-center overflow-hidden\">\n                  {session.user?.image ? (\n                    <img\n                      src={session.user.image}\n                      alt={session.user.name || ''}\n                      className=\"w-full h-full object-cover\"\n                    />\n                  ) : (\n                    <span className=\"text-lg font-medium text-gray-600\">\n                      {session.user?.name?.charAt(0) || 'U'}\n                    </span>\n                  )}\n                </div>\n                <div>\n                  <p className=\"font-medium text-sm\">\n                    {session.user?.name}\n                  </p>\n                  <p className=\"text-gray-500 text-xs\">\n                    {session.user?.email}\n                  </p>\n                  {session.user?.role === 'admin' && (\n                    <span className=\"inline-block mt-1 px-2 py-1 text-xs bg-red-100 text-red-800 rounded\">\n                      {t('admin')}\n                    </span>\n                  )}\n                </div>\n              </div>\n            </div>\n\n            {/* 用户功能 */}\n            <div className=\"py-2\">\n              <NavLink href=\"/profile\" locale={locale}>\n                 <FaUser /> \n                 {t('profile')}\n              </NavLink>\n             \n             <NavLink href=\"/profile/submitted\" locale={locale}>\n                <FaList />\n                {t('my_submissions')}\n              </NavLink>\n\n              <NavLink href=\"/profile/liked\" locale={locale}>\n                <FaHeart />\n                {t('my_favorites')}\n              </NavLink>\n\n              <NavLink href=\"/submit\" locale={locale}>\n                  <FaPlus />\n                  {t('submit_tool')}\n              </NavLink>\n            </div>\n\n            {/* 管理员功能 */}\n            {session.user?.role === 'admin' && (\n              <div className=\"border-t py-2\">\n                <NavLink href=\"/admin\" locale={locale}>\n                  <FaCog />\n                  {t('admin_panel')}\n                </NavLink>\n              </div>\n            )}\n\n            {/* 设置和登出 */}\n            <div className=\"border-t py-2\">\n            <NavLink href=\"/settings\" locale={locale}>\n              <FaCog />\n              {t('settings')}\n            </NavLink>\n            </div>\n          </div>\n        </>\n      )}\n    </div>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AACA;AACA;;;AAPA;;;;;;;AAWA,MAAM,UAAU,CAAC,EAAE,QAAQ,EAAE,IAAI,EAAE,MAAM,EAA+D,iBACtG,6LAAC,yHAAA,CAAA,OAAQ;QACP,MAAM;QACN,QAAQ;QACR,WAAU;kBAET;;;;;;KANC;AAUS,SAAS,eAAe,EAAE,MAAM,EAAsB;;IACnE,MAAM,EAAE,MAAM,OAAO,EAAE,MAAM,EAAE,GAAG,CAAA,GAAA,iJAAA,CAAA,aAAU,AAAD;IAC3C,MAAM,SAAS,CAAA,GAAA,yHAAA,CAAA,YAAS,AAAD;IACvB,MAAM,CAAC,kBAAkB,oBAAoB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACzD,MAAM,CAAC,YAAY,cAAc,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAE7C,MAAM,IAAI,CAAA,GAAA,yMAAA,CAAA,kBAAe,AAAD,EAAE;IAE1B,MAAM,gBAAgB;QACpB,MAAM,CAAA,GAAA,iJAAA,CAAA,UAAO,AAAD,EAAE;YAAE,aAAa;QAAI;IACnC;IAEA,MAAM,mBAAmB,CAAC;QACxB,cAAc;QACd,OAAO,IAAI,CAAC;IACd;IAEA,gBAAgB;IAChB,IAAI,WAAW,WAAW;QACxB,qBACE,6LAAC;YACC,WAAU;YACV,QAAQ;sBAEP,EAAE;;;;;;IAGT;IAEA,eAAe;IACf,IAAI,CAAC,SAAS;QACZ,qBACE;;8BACE,6LAAC;oBACC,WAAU;oBACV,SAAS,IAAM,oBAAoB;;sCAEnC,6LAAC,iJAAA,CAAA,cAAW;;;;;wBACX,EAAE;;;;;;;8BAEL,6LAAC,2IAAA,CAAA,UAAU;oBAAC,QAAQ;oBAAkB,SAAS,IAAM,oBAAoB;;;;;;;;IAG/E;IAEA,eAAe;IACf,qBACE,6LAAC;QAAI,WAAU;;0BACb,6LAAC;gBACC,WAAU;gBACV,SAAS,IAAM,cAAc,CAAC;;kCAE9B,6LAAC;wBAAI,WAAU;kCACZ,QAAQ,IAAI,EAAE,sBACb,6LAAC;4BACC,KAAK,QAAQ,IAAI,CAAC,KAAK;4BACvB,KAAK,QAAQ,IAAI,CAAC,IAAI,IAAI;4BAC1B,WAAU;;;;;iDAGZ,6LAAC;4BAAK,WAAU;sCACb,QAAQ,IAAI,EAAE,MAAM,OAAO,MAAM;;;;;;;;;;;kCAIxC,6LAAC;wBAAK,WAAU;kCACb,QAAQ,IAAI,EAAE;;;;;;kCAEjB,6LAAC,iJAAA,CAAA,gBAAa;wBAAC,WAAW,CAAC,6BAA6B,EAAE,aAAa,eAAe,IAAI;;;;;;;;;;;;YAG3F,4BACC;;kCAEE,6LAAC;wBACC,WAAU;wBACV,SAAS,IAAM,cAAc;;;;;;kCAI/B,6LAAC;wBACC,WAAU;wBACV,SAAS,IAAM,cAAc,CAAC;;0CAG9B,6LAAC;gCAAI,WAAU;0CACb,cAAA,6LAAC;oCAAI,WAAU;;sDACb,6LAAC;4CAAI,WAAU;sDACZ,QAAQ,IAAI,EAAE,sBACb,6LAAC;gDACC,KAAK,QAAQ,IAAI,CAAC,KAAK;gDACvB,KAAK,QAAQ,IAAI,CAAC,IAAI,IAAI;gDAC1B,WAAU;;;;;qEAGZ,6LAAC;gDAAK,WAAU;0DACb,QAAQ,IAAI,EAAE,MAAM,OAAO,MAAM;;;;;;;;;;;sDAIxC,6LAAC;;8DACC,6LAAC;oDAAE,WAAU;8DACV,QAAQ,IAAI,EAAE;;;;;;8DAEjB,6LAAC;oDAAE,WAAU;8DACV,QAAQ,IAAI,EAAE;;;;;;gDAEhB,QAAQ,IAAI,EAAE,SAAS,yBACtB,6LAAC;oDAAK,WAAU;8DACb,EAAE;;;;;;;;;;;;;;;;;;;;;;;0CAQb,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;wCAAQ,MAAK;wCAAW,QAAQ;;0DAC9B,6LAAC,iJAAA,CAAA,SAAM;;;;;4CACN,EAAE;;;;;;;kDAGP,6LAAC;wCAAQ,MAAK;wCAAqB,QAAQ;;0DACxC,6LAAC,iJAAA,CAAA,SAAM;;;;;4CACN,EAAE;;;;;;;kDAGL,6LAAC;wCAAQ,MAAK;wCAAiB,QAAQ;;0DACrC,6LAAC,iJAAA,CAAA,UAAO;;;;;4CACP,EAAE;;;;;;;kDAGL,6LAAC;wCAAQ,MAAK;wCAAU,QAAQ;;0DAC5B,6LAAC,iJAAA,CAAA,SAAM;;;;;4CACN,EAAE;;;;;;;;;;;;;4BAKR,QAAQ,IAAI,EAAE,SAAS,yBACtB,6LAAC;gCAAI,WAAU;0CACb,cAAA,6LAAC;oCAAQ,MAAK;oCAAS,QAAQ;;sDAC7B,6LAAC,iJAAA,CAAA,QAAK;;;;;wCACL,EAAE;;;;;;;;;;;;0CAMT,6LAAC;gCAAI,WAAU;0CACf,cAAA,6LAAC;oCAAQ,MAAK;oCAAY,QAAQ;;sDAChC,6LAAC,iJAAA,CAAA,QAAK;;;;;wCACL,EAAE;;;;;;;;;;;;;;;;;;;;;;;;;;AAQjB;GAjKwB;;QACY,iJAAA,CAAA,aAAU;QAC7B,yHAAA,CAAA,YAAS;QAId,yMAAA,CAAA,kBAAe;;;MANH", "debugId": null}}, {"offset": {"line": 961, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/workspace/aitools/aitools-website/src/i18n/config.ts"], "sourcesContent": ["// 支持的语言列表\nexport const locales = ['en', 'zh'] as const;\nexport type Locale = (typeof locales)[number];\n\n// 默认语言\nexport const defaultLocale: Locale = 'en';\n\n// 语言显示名称\nexport const localeNames: Record<Locale, string> = {\n  zh: '中文',\n  en: 'English',\n};\n\n// 验证语言是否有效\nexport function isValidLocale(locale: string): locale is Locale {\n  return locales.includes(locale as Locale);\n}\n"], "names": [], "mappings": "AAAA,UAAU;;;;;;;AACH,MAAM,UAAU;IAAC;IAAM;CAAK;AAI5B,MAAM,gBAAwB;AAG9B,MAAM,cAAsC;IACjD,IAAI;IACJ,IAAI;AACN;AAGO,SAAS,cAAc,MAAc;IAC1C,OAAO,QAAQ,QAAQ,CAAC;AAC1B", "debugId": null}}, {"offset": {"line": 989, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/workspace/aitools/aitools-website/src/components/layout/LanguageSwitcherClient.tsx"], "sourcesContent": ["'use client';\n\nimport { useState } from 'react';\nimport { useRouter } from 'next/navigation';\nimport { usePathname } from 'next/navigation';\nimport { FaGlobe } from 'react-icons/fa';\nimport { locales, localeNames, type Locale } from '@/i18n/config';\n\ninterface LanguageSwitcherClientProps {\n  currentLocale: Locale;\n}\n\nexport default function LanguageSwitcherClient({ currentLocale }: LanguageSwitcherClientProps) {\n  const [isOpen, setIsOpen] = useState(false);\n  const router = useRouter();\n  const pathname = usePathname();\n\n  const switchLanguage = (newLocale: Locale) => {\n    // 解析当前路径，移除语言前缀\n    const segments = pathname?.split('/')?.filter(Boolean) || [];\n    \n    // 如果第一段是语言代码，则移除它\n    if (segments.length > 0 && locales.includes(segments[0] as Locale)) {\n      segments.shift();\n    }\n    \n    // 构建新路径\n    const newPath = `/${newLocale}${segments.length > 0 ? '/' + segments.join('/') : ''}`;\n    \n    // 导航到新路径\n    router.replace(newPath);\n    setIsOpen(false);\n  };\n\n  return (\n    <div className=\"relative\">\n      <button\n        onClick={() => setIsOpen(!isOpen)}\n        className=\"flex items-center space-x-1 px-3 py-2 text-gray-700 hover:text-gray-900 hover:bg-gray-100 rounded-md transition-colors\"\n      >\n        <FaGlobe className=\"w-4 h-4\" />\n        <span className=\"text-sm\">{localeNames[currentLocale]}</span>\n      </button>\n\n      {isOpen && (\n        <div className=\"absolute right-0 mt-2 w-32 bg-white rounded-md shadow-lg border border-gray-200 z-50\">\n          {locales.map((locale) => (\n            <button\n              key={locale}\n              onClick={() => switchLanguage(locale)}\n              className={`block w-full text-left px-4 py-2 text-sm hover:bg-gray-100 transition-colors ${\n                locale === currentLocale ? 'bg-blue-50 text-blue-600' : 'text-gray-700'\n              }`}\n            >\n              {localeNames[locale]}\n            </button>\n          ))}\n        </div>\n      )}\n    </div>\n  );\n}"], "names": [], "mappings": ";;;;AAEA;AACA;AAEA;AACA;;;AANA;;;;;;AAYe,SAAS,uBAAuB,EAAE,aAAa,EAA+B;;IAC3F,MAAM,CAAC,QAAQ,UAAU,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACrC,MAAM,SAAS,CAAA,GAAA,qIAAA,CAAA,YAAS,AAAD;IACvB,MAAM,WAAW,CAAA,GAAA,qIAAA,CAAA,cAAW,AAAD;IAE3B,MAAM,iBAAiB,CAAC;QACtB,gBAAgB;QAChB,MAAM,WAAW,UAAU,MAAM,MAAM,OAAO,YAAY,EAAE;QAE5D,kBAAkB;QAClB,IAAI,SAAS,MAAM,GAAG,KAAK,wHAAA,CAAA,UAAO,CAAC,QAAQ,CAAC,QAAQ,CAAC,EAAE,GAAa;YAClE,SAAS,KAAK;QAChB;QAEA,QAAQ;QACR,MAAM,UAAU,CAAC,CAAC,EAAE,YAAY,SAAS,MAAM,GAAG,IAAI,MAAM,SAAS,IAAI,CAAC,OAAO,IAAI;QAErF,SAAS;QACT,OAAO,OAAO,CAAC;QACf,UAAU;IACZ;IAEA,qBACE,6LAAC;QAAI,WAAU;;0BACb,6LAAC;gBACC,SAAS,IAAM,UAAU,CAAC;gBAC1B,WAAU;;kCAEV,6LAAC,iJAAA,CAAA,UAAO;wBAAC,WAAU;;;;;;kCACnB,6LAAC;wBAAK,WAAU;kCAAW,wHAAA,CAAA,cAAW,CAAC,cAAc;;;;;;;;;;;;YAGtD,wBACC,6LAAC;gBAAI,WAAU;0BACZ,wHAAA,CAAA,UAAO,CAAC,GAAG,CAAC,CAAC,uBACZ,6LAAC;wBAEC,SAAS,IAAM,eAAe;wBAC9B,WAAW,CAAC,6EAA6E,EACvF,WAAW,gBAAgB,6BAA6B,iBACxD;kCAED,wHAAA,CAAA,cAAW,CAAC,OAAO;uBANf;;;;;;;;;;;;;;;;AAanB;GAjDwB;;QAEP,qIAAA,CAAA,YAAS;QACP,qIAAA,CAAA,cAAW;;;KAHN", "debugId": null}}, {"offset": {"line": 1092, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/workspace/aitools/aitools-website/src/components/layout/SearchFormClient.tsx"], "sourcesContent": ["'use client';\n\nimport { useRouter } from '@/i18n/routing';\nimport { useTranslations } from 'next-intl';\nimport { FaSearch } from 'react-icons/fa';\n\ninterface SearchFormClientProps {\n  locale: string;\n  className?: string;\n}\n\nexport default function SearchFormClient({ locale, className = \"\" }: SearchFormClientProps) {\n  const router = useRouter();\n  const t = useTranslations('navigation');\n\n  const handleSearch = (e: React.FormEvent<HTMLFormElement>) => {\n    e.preventDefault();\n    const formData = new FormData(e.currentTarget);\n    const query = formData.get('search') as string;\n    if (query.trim()) {\n      router.push(`/search?q=${encodeURIComponent(query.trim())}`);\n    }\n  };\n\n  return (\n    <form onSubmit={handleSearch} className={className}>\n      <div className=\"relative\">\n        <div className=\"absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none\">\n          <FaSearch className=\"text-gray-400\" />\n        </div>\n        <input\n          name=\"search\"\n          type=\"text\"\n          placeholder={t('search_placeholder')}\n          className=\"w-full pl-10 pr-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent\"\n        />\n      </div>\n    </form>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;;;AAJA;;;;AAWe,SAAS,iBAAiB,EAAE,MAAM,EAAE,YAAY,EAAE,EAAyB;;IACxF,MAAM,SAAS,CAAA,GAAA,yHAAA,CAAA,YAAS,AAAD;IACvB,MAAM,IAAI,CAAA,GAAA,yMAAA,CAAA,kBAAe,AAAD,EAAE;IAE1B,MAAM,eAAe,CAAC;QACpB,EAAE,cAAc;QAChB,MAAM,WAAW,IAAI,SAAS,EAAE,aAAa;QAC7C,MAAM,QAAQ,SAAS,GAAG,CAAC;QAC3B,IAAI,MAAM,IAAI,IAAI;YAChB,OAAO,IAAI,CAAC,CAAC,UAAU,EAAE,mBAAmB,MAAM,IAAI,KAAK;QAC7D;IACF;IAEA,qBACE,6LAAC;QAAK,UAAU;QAAc,WAAW;kBACvC,cAAA,6LAAC;YAAI,WAAU;;8BACb,6LAAC;oBAAI,WAAU;8BACb,cAAA,6LAAC,iJAAA,CAAA,WAAQ;wBAAC,WAAU;;;;;;;;;;;8BAEtB,6LAAC;oBACC,MAAK;oBACL,MAAK;oBACL,aAAa,EAAE;oBACf,WAAU;;;;;;;;;;;;;;;;;AAKpB;GA5BwB;;QACP,yHAAA,CAAA,YAAS;QACd,yMAAA,CAAA,kBAAe;;;KAFH", "debugId": null}}, {"offset": {"line": 1177, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/workspace/aitools/aitools-website/src/components/layout/MobileMenuClient.tsx"], "sourcesContent": ["'use client';\n\nimport { useState } from 'react';\nimport { Link as NextLink } from '@/i18n/routing';\nimport { FaBars, FaTimes } from 'react-icons/fa';\nimport SearchFormClient from './SearchFormClient';\n\ninterface MobileMenuClientProps {\n  links: Array<{ name: string; href: string }>;\n  locale: string;\n}\n\nconst NavLink = ({ children, href, locale }: { children: React.ReactNode; href: string; locale: string }) => (\n  <NextLink\n    href={href}\n    locale={locale}\n    className=\"px-2 py-1 rounded-md text-gray-700 hover:text-gray-900 hover:bg-gray-200 transition-colors\"\n  >\n    {children}\n  </NextLink>\n);\n\nexport default function MobileMenuClient({ links, locale }: MobileMenuClientProps) {\n  const [isMobileMenuOpen, setIsMobileMenuOpen] = useState(false);\n\n  return (\n    <>\n      {/* Mobile menu button */}\n      <button\n        className=\"md:hidden ml-2 p-2 rounded-md text-gray-400 hover:text-gray-500 hover:bg-gray-100\"\n        onClick={() => setIsMobileMenuOpen(!isMobileMenuOpen)}\n        aria-label=\"Open Menu\"\n      >\n        {isMobileMenuOpen ? <FaTimes /> : <FaBars />}\n      </button>\n\n      {/* Mobile Navigation */}\n      {isMobileMenuOpen && (\n        <div className=\"md:hidden pb-4\">\n          <nav className=\"space-y-4\">\n            {links.map((link) => (\n              <NavLink key={link.name} href={link.href} locale={locale}>\n                {link.name}\n              </NavLink>\n            ))}\n\n            {/* Mobile Search */}\n            <div className=\"pt-4\">\n              <SearchFormClient locale={locale} />\n            </div>\n          </nav>\n        </div>\n      )}\n    </>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;;;AALA;;;;;AAYA,MAAM,UAAU,CAAC,EAAE,QAAQ,EAAE,IAAI,EAAE,MAAM,EAA+D,iBACtG,6LAAC,yHAAA,CAAA,OAAQ;QACP,MAAM;QACN,QAAQ;QACR,WAAU;kBAET;;;;;;KANC;AAUS,SAAS,iBAAiB,EAAE,KAAK,EAAE,MAAM,EAAyB;;IAC/E,MAAM,CAAC,kBAAkB,oBAAoB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAEzD,qBACE;;0BAEE,6LAAC;gBACC,WAAU;gBACV,SAAS,IAAM,oBAAoB,CAAC;gBACpC,cAAW;0BAEV,iCAAmB,6LAAC,iJAAA,CAAA,UAAO;;;;yCAAM,6LAAC,iJAAA,CAAA,SAAM;;;;;;;;;;YAI1C,kCACC,6LAAC;gBAAI,WAAU;0BACb,cAAA,6LAAC;oBAAI,WAAU;;wBACZ,MAAM,GAAG,CAAC,CAAC,qBACV,6LAAC;gCAAwB,MAAM,KAAK,IAAI;gCAAE,QAAQ;0CAC/C,KAAK,IAAI;+BADE,KAAK,IAAI;;;;;sCAMzB,6LAAC;4BAAI,WAAU;sCACb,cAAA,6LAAC,mJAAA,CAAA,UAAgB;gCAAC,QAAQ;;;;;;;;;;;;;;;;;;;;;;;;AAOxC;GAjCwB;MAAA", "debugId": null}}, {"offset": {"line": 1282, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/workspace/aitools/aitools-website/src/contexts/LikeContext.tsx"], "sourcesContent": ["'use client';\n\nimport React, { createContext, useContext, useState, useCallback, useEffect } from 'react';\nimport { useSession } from 'next-auth/react';\n\n// 工具点赞状态接口\ninterface ToolLikeState {\n  liked: boolean;\n  likes: number;\n  loading: boolean;\n}\n\n// Context状态接口\ninterface LikeContextState {\n  // 工具点赞状态映射 toolId -> ToolLikeState\n  toolStates: Record<string, ToolLikeState>;\n  // 切换点赞状态\n  toggleLike: (toolId: string, forceUnlike?: boolean) => Promise<boolean>;\n  // 获取工具点赞状态\n  getToolState: (toolId: string) => ToolLikeState;\n  // 初始化工具状态\n  initializeToolState: (toolId: string, initialLikes: number, initialLiked?: boolean) => void;\n  // 刷新工具状态\n  refreshToolState: (toolId: string) => Promise<void>;\n}\n\n// 默认工具状态\nconst defaultToolState: ToolLikeState = {\n  liked: false,\n  likes: 0,\n  loading: false\n};\n\n// 创建Context\nconst LikeContext = createContext<LikeContextState | null>(null);\n\n// Provider组件\nexport function LikeProvider({ children }: { children: React.ReactNode }) {\n  const { data: session } = useSession();\n  const [toolStates, setToolStates] = useState<Record<string, ToolLikeState>>({});\n\n  // 获取工具状态\n  const getToolState = useCallback((toolId: string): ToolLikeState => {\n    return toolStates[toolId] || defaultToolState;\n  }, [toolStates]);\n\n  // 初始化工具状态\n  const initializeToolState = useCallback((toolId: string, initialLikes: number, initialLiked = false) => {\n    setToolStates(prev => {\n      // 如果已经存在状态，不覆盖（避免重复初始化）\n      if (prev[toolId]) {\n        return prev;\n      }\n      return {\n        ...prev,\n        [toolId]: {\n          liked: initialLiked,\n          likes: initialLikes,\n          loading: false\n        }\n      };\n    });\n  }, []);\n\n  // 刷新工具状态（从服务器获取最新状态）\n  const refreshToolState = useCallback(async (toolId: string) => {\n    if (!session) return;\n\n    try {\n      const response = await fetch(`/api/tools/${toolId}/like`);\n      if (response.ok) {\n        const data = await response.json();\n        if (data.success) {\n          setToolStates(prev => ({\n            ...prev,\n            [toolId]: {\n              liked: data.data.liked,\n              likes: data.data.likes,\n              loading: false\n            }\n          }));\n        }\n      }\n    } catch (error) {\n      console.error('Failed to refresh tool state:', error);\n    }\n  }, [session]);\n\n  // 切换点赞状态\n  const toggleLike = useCallback(async (toolId: string, forceUnlike = false): Promise<boolean> => {\n    if (!session) {\n      return false; // 需要登录\n    }\n\n    // 设置加载状态\n    setToolStates(prev => ({\n      ...prev,\n      [toolId]: {\n        ...prev[toolId] || defaultToolState,\n        loading: true\n      }\n    }));\n\n    try {\n      const requestBody = forceUnlike ? { forceUnlike: true } : {};\n      \n      const response = await fetch(`/api/tools/${toolId}/like`, {\n        method: 'POST',\n        headers: {\n          'Content-Type': 'application/json',\n        },\n        body: JSON.stringify(requestBody),\n      });\n\n      if (response.ok) {\n        const data = await response.json();\n        if (data.success) {\n          // 更新状态\n          setToolStates(prev => ({\n            ...prev,\n            [toolId]: {\n              liked: data.data.liked,\n              likes: data.data.likes,\n              loading: false\n            }\n          }));\n          return true; // 操作成功\n        }\n      }\n      \n      // 操作失败，恢复加载状态\n      setToolStates(prev => ({\n        ...prev,\n        [toolId]: {\n          ...prev[toolId] || defaultToolState,\n          loading: false\n        }\n      }));\n      return false;\n    } catch (error) {\n      console.error('Like request failed:', error);\n      // 操作失败，恢复加载状态\n      setToolStates(prev => ({\n        ...prev,\n        [toolId]: {\n          ...prev[toolId] || defaultToolState,\n          loading: false\n        }\n      }));\n      return false;\n    }\n  }, [session]);\n\n  // 当用户登录状态改变时，刷新所有已初始化的工具状态\n  useEffect(() => {\n    if (session) {\n      // 用户登录后，刷新所有工具状态\n      const toolIds = Object.keys(toolStates);\n      toolIds.forEach(toolId => {\n        refreshToolState(toolId);\n      });\n    } else {\n      // 用户登出后，重置所有点赞状态为false\n      setToolStates(prev => {\n        const newStates: Record<string, ToolLikeState> = {};\n        Object.keys(prev).forEach(toolId => {\n          newStates[toolId] = {\n            ...prev[toolId],\n            liked: false,\n            loading: false\n          };\n        });\n        return newStates;\n      });\n    }\n  }, [session]); // 移除refreshToolState依赖，避免无限循环\n\n  const contextValue: LikeContextState = {\n    toolStates,\n    toggleLike,\n    getToolState,\n    initializeToolState,\n    refreshToolState\n  };\n\n  return (\n    <LikeContext.Provider value={contextValue}>\n      {children}\n    </LikeContext.Provider>\n  );\n}\n\n// Hook for using the context\nexport function useLike() {\n  const context = useContext(LikeContext);\n  if (!context) {\n    throw new Error('useLike must be used within a LikeProvider');\n  }\n  return context;\n}\n"], "names": [], "mappings": ";;;;;AAEA;AACA;;;AAHA;;;AA0BA,SAAS;AACT,MAAM,mBAAkC;IACtC,OAAO;IACP,OAAO;IACP,SAAS;AACX;AAEA,YAAY;AACZ,MAAM,4BAAc,CAAA,GAAA,6JAAA,CAAA,gBAAa,AAAD,EAA2B;AAGpD,SAAS,aAAa,EAAE,QAAQ,EAAiC;;IACtE,MAAM,EAAE,MAAM,OAAO,EAAE,GAAG,CAAA,GAAA,iJAAA,CAAA,aAAU,AAAD;IACnC,MAAM,CAAC,YAAY,cAAc,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAiC,CAAC;IAE7E,SAAS;IACT,MAAM,eAAe,CAAA,GAAA,6JAAA,CAAA,cAAW,AAAD;kDAAE,CAAC;YAChC,OAAO,UAAU,CAAC,OAAO,IAAI;QAC/B;iDAAG;QAAC;KAAW;IAEf,UAAU;IACV,MAAM,sBAAsB,CAAA,GAAA,6JAAA,CAAA,cAAW,AAAD;yDAAE,CAAC,QAAgB,cAAsB,eAAe,KAAK;YACjG;iEAAc,CAAA;oBACZ,wBAAwB;oBACxB,IAAI,IAAI,CAAC,OAAO,EAAE;wBAChB,OAAO;oBACT;oBACA,OAAO;wBACL,GAAG,IAAI;wBACP,CAAC,OAAO,EAAE;4BACR,OAAO;4BACP,OAAO;4BACP,SAAS;wBACX;oBACF;gBACF;;QACF;wDAAG,EAAE;IAEL,qBAAqB;IACrB,MAAM,mBAAmB,CAAA,GAAA,6JAAA,CAAA,cAAW,AAAD;sDAAE,OAAO;YAC1C,IAAI,CAAC,SAAS;YAEd,IAAI;gBACF,MAAM,WAAW,MAAM,MAAM,CAAC,WAAW,EAAE,OAAO,KAAK,CAAC;gBACxD,IAAI,SAAS,EAAE,EAAE;oBACf,MAAM,OAAO,MAAM,SAAS,IAAI;oBAChC,IAAI,KAAK,OAAO,EAAE;wBAChB;0EAAc,CAAA,OAAQ,CAAC;oCACrB,GAAG,IAAI;oCACP,CAAC,OAAO,EAAE;wCACR,OAAO,KAAK,IAAI,CAAC,KAAK;wCACtB,OAAO,KAAK,IAAI,CAAC,KAAK;wCACtB,SAAS;oCACX;gCACF,CAAC;;oBACH;gBACF;YACF,EAAE,OAAO,OAAO;gBACd,QAAQ,KAAK,CAAC,iCAAiC;YACjD;QACF;qDAAG;QAAC;KAAQ;IAEZ,SAAS;IACT,MAAM,aAAa,CAAA,GAAA,6JAAA,CAAA,cAAW,AAAD;gDAAE,OAAO,QAAgB,cAAc,KAAK;YACvE,IAAI,CAAC,SAAS;gBACZ,OAAO,OAAO,OAAO;YACvB;YAEA,SAAS;YACT;wDAAc,CAAA,OAAQ,CAAC;wBACrB,GAAG,IAAI;wBACP,CAAC,OAAO,EAAE;4BACR,GAAG,IAAI,CAAC,OAAO,IAAI,gBAAgB;4BACnC,SAAS;wBACX;oBACF,CAAC;;YAED,IAAI;gBACF,MAAM,cAAc,cAAc;oBAAE,aAAa;gBAAK,IAAI,CAAC;gBAE3D,MAAM,WAAW,MAAM,MAAM,CAAC,WAAW,EAAE,OAAO,KAAK,CAAC,EAAE;oBACxD,QAAQ;oBACR,SAAS;wBACP,gBAAgB;oBAClB;oBACA,MAAM,KAAK,SAAS,CAAC;gBACvB;gBAEA,IAAI,SAAS,EAAE,EAAE;oBACf,MAAM,OAAO,MAAM,SAAS,IAAI;oBAChC,IAAI,KAAK,OAAO,EAAE;wBAChB,OAAO;wBACP;oEAAc,CAAA,OAAQ,CAAC;oCACrB,GAAG,IAAI;oCACP,CAAC,OAAO,EAAE;wCACR,OAAO,KAAK,IAAI,CAAC,KAAK;wCACtB,OAAO,KAAK,IAAI,CAAC,KAAK;wCACtB,SAAS;oCACX;gCACF,CAAC;;wBACD,OAAO,MAAM,OAAO;oBACtB;gBACF;gBAEA,cAAc;gBACd;4DAAc,CAAA,OAAQ,CAAC;4BACrB,GAAG,IAAI;4BACP,CAAC,OAAO,EAAE;gCACR,GAAG,IAAI,CAAC,OAAO,IAAI,gBAAgB;gCACnC,SAAS;4BACX;wBACF,CAAC;;gBACD,OAAO;YACT,EAAE,OAAO,OAAO;gBACd,QAAQ,KAAK,CAAC,wBAAwB;gBACtC,cAAc;gBACd;4DAAc,CAAA,OAAQ,CAAC;4BACrB,GAAG,IAAI;4BACP,CAAC,OAAO,EAAE;gCACR,GAAG,IAAI,CAAC,OAAO,IAAI,gBAAgB;gCACnC,SAAS;4BACX;wBACF,CAAC;;gBACD,OAAO;YACT;QACF;+CAAG;QAAC;KAAQ;IAEZ,2BAA2B;IAC3B,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;kCAAE;YACR,IAAI,SAAS;gBACX,iBAAiB;gBACjB,MAAM,UAAU,OAAO,IAAI,CAAC;gBAC5B,QAAQ,OAAO;8CAAC,CAAA;wBACd,iBAAiB;oBACnB;;YACF,OAAO;gBACL,uBAAuB;gBACvB;8CAAc,CAAA;wBACZ,MAAM,YAA2C,CAAC;wBAClD,OAAO,IAAI,CAAC,MAAM,OAAO;sDAAC,CAAA;gCACxB,SAAS,CAAC,OAAO,GAAG;oCAClB,GAAG,IAAI,CAAC,OAAO;oCACf,OAAO;oCACP,SAAS;gCACX;4BACF;;wBACA,OAAO;oBACT;;YACF;QACF;iCAAG;QAAC;KAAQ,GAAG,8BAA8B;IAE7C,MAAM,eAAiC;QACrC;QACA;QACA;QACA;QACA;IACF;IAEA,qBACE,6LAAC,YAAY,QAAQ;QAAC,OAAO;kBAC1B;;;;;;AAGP;GAzJgB;;QACY,iJAAA,CAAA,aAAU;;;KADtB;AA4JT,SAAS;;IACd,MAAM,UAAU,CAAA,GAAA,6JAAA,CAAA,aAAU,AAAD,EAAE;IAC3B,IAAI,CAAC,SAAS;QACZ,MAAM,IAAI,MAAM;IAClB;IACA,OAAO;AACT;IANgB", "debugId": null}}]}